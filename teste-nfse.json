{"Id": "RPS_TESTE_001", "Numero": 1, "Serie": "001", "Tipo": 1, "DataEmissao": "2025-07-12T04:27:32.805Z", "NaturezaOperacao": 1, "OptanteSimplesNacional": 1, "IncentivadorCultural": 2, "Status": 1, "Servico": {"ValorServicos": 1000.0, "ValorDeducoes": 0.0, "ValorPis": 0.0, "ValorCofins": 0.0, "ValorInss": 0.0, "ValorIr": 0.0, "ValorCsll": 0.0, "IssRetido": 2, "ValorIss": 50.0, "ValorIssRetido": 0.0, "OutrasRetencoes": 0.0, "BaseCalculo": 1000.0, "Aliquota": 5.0, "ValorLiquidoNfse": 1000.0, "DescontoIncondicionado": 0.0, "DescontoCondicionado": 0.0, "ItemListaServico": "01.01", "CodigoCnae": "6201500", "CodigoTributacaoMunicipio": "010101", "Discriminacao": "Desenvolvimento de software sob medida para teste", "CodigoMunicipio": "3550308"}, "Prestador": {"Cnpj": "01001001000113", "InscricaoMunicipal": "15000"}, "Tomador": {"Cnpj": "98765432000198", "InscricaoMunicipal": "654321", "RazaoSocial": "Empresa Tomadora LTDA", "Endereco": {"Endereco": "<PERSON><PERSON>", "Numero": "123", "Complemento": "Sala 1", "Bairro": "Centro", "CodigoMunicipio": "3550308", "Uf": "SP", "Cep": "01234567"}, "Contato": {"Telefone": "11999999999", "Email": "<EMAIL>"}}}