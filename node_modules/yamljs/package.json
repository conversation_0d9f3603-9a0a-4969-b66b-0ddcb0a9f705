{"name": "yamljs", "version": "0.3.0", "description": "Standalone JavaScript YAML 1.2 Parser & Encoder. Works under node.js and all major browsers. Also brings command line YAML/JSON conversion tools.", "keywords": ["yaml", "json", "yaml2j<PERSON>", "json2yaml"], "author": "<PERSON> <<EMAIL>>", "main": "./lib/Yaml.js", "dependencies": {"argparse": "^1.0.7", "glob": "^7.0.5"}, "devDependencies": {"benchmark": "^2.1.0", "coffeeify": "^2.0.1", "jasmine-node": "^1.14.5"}, "bin": {"yaml2json": "./bin/yaml2json", "json2yaml": "./bin/json2yaml"}, "scripts": {"test": "cake build; cake test"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/jeremyfa/yaml.js.git"}}