{"version": 3, "file": "nscontext.js", "sourceRoot": "", "sources": ["../src/nscontext.ts"], "names": [], "mappings": ";;;AAOA;;;;;GAKG;AACH,MAAM,cAAc;IAIlB,YAAY,MAAsB;QAChC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;IACvB,CAAC;IAED;;;;;OAKG;IACI,eAAe,CAAC,MAAc,EAAE,SAAmB;QACxD,QAAQ,MAAM,EAAE;YACd,KAAK,KAAK;gBACR,OAAO,sCAAsC,CAAC;YAChD,KAAK,OAAO;gBACV,OAAO,+BAA+B,CAAC;YACzC;gBACE,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;gBACtC,iBAAiB;gBACjB,IAAI,KAAK,IAAI,IAAI,EAAE;oBACjB,OAAO,KAAK,CAAC,GAAG,CAAC;iBAClB;qBAAM,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,EAAE;oBACpC,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;iBAC5C;qBAAM;oBACL,OAAO,IAAI,CAAC;iBACb;SACJ;IACH,CAAC;IAEM,mBAAmB,CAAC,MAAc;QACvC,QAAQ,MAAM,EAAE;YACd,KAAK,KAAK;gBACR,OAAO;oBACL,GAAG,EAAE,sCAAsC;oBAC3C,MAAM,EAAE,KAAK;oBACb,QAAQ,EAAE,IAAI;iBACf,CAAC;YACJ,KAAK,OAAO;gBACV,OAAO;oBACL,GAAG,EAAE,+BAA+B;oBACpC,MAAM,EAAE,OAAO;oBACf,QAAQ,EAAE,IAAI;iBACf,CAAC;YACJ;gBACE,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;gBACxC,iBAAiB;gBACjB,IAAI,OAAO,IAAI,IAAI,EAAE;oBACnB,OAAO,OAAO,CAAC;iBAChB;qBAAM,IAAI,IAAI,CAAC,MAAM,EAAE;oBACtB,OAAO,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;iBAChD;qBAAM;oBACL,OAAO,IAAI,CAAC;iBACb;SACJ;IACH,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,KAAa,EAAE,SAAmB;QACjD,QAAQ,KAAK,EAAE;YACb,KAAK,sCAAsC;gBACzC,OAAO,KAAK,CAAC;YACf,KAAK,+BAA+B;gBAClC,OAAO,OAAO,CAAC;YACjB;gBACE,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE;oBAC/B,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,KAAK,EAAE;wBACpC,OAAO,CAAC,CAAC;qBACV;iBACF;gBACD,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,EAAE;oBAC7B,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;iBACrC;qBAAM;oBACL,OAAO,IAAI,CAAC;iBACb;SACJ;IACH,CAAC;CACF;AAED;;;;GAIG;AACH,MAAa,gBAAgB;IAK3B;QACE,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;IACvB,CAAC;IAED;;;;;;;OAOG;IACI,YAAY,CAAC,MAAc,EAAE,KAAa,EAAE,SAAmB;QACpE,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,SAAS,CAAC,KAAK,KAAK,EAAE;YACrD,OAAO,KAAK,CAAC;SACd;QACD,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG;gBACrC,GAAG,EAAE,KAAK;gBACV,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,KAAK;aAChB,CAAC;YACF,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;OAGG;IACI,WAAW;QAChB,MAAM,KAAK,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACpD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;OAGG;IACI,UAAU;QACf,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;QAChC,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC;SAClC;aAAM;YACL,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC1B;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;OAKG;IACI,eAAe,CAAC,MAAc,EAAE,SAAmB;QACxD,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;IACnF,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,KAAa,EAAE,SAAmB;QACjD,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;IAC5E,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,KAAa;QACpC,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,MAAM,EAAE;YACV,kDAAkD;YAClD,OAAO,MAAM,CAAC;SACf;aAAM;YACL,qCAAqC;YACrC,OAAO,IAAI,EAAE;gBACX,MAAM,GAAG,IAAI,GAAG,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;gBACrC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE;oBACjC,yBAAyB;oBACzB,MAAM;iBACP;aACF;SACF;QACD,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACvC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,gBAAgB,CAAC,MAAc,EAAE,KAAa;QACnD,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAC9D,IAAI,OAAO,IAAI,OAAO,CAAC,GAAG,KAAK,KAAK,IAAI,OAAO,CAAC,QAAQ,EAAE;gBACxD,OAAO,KAAK,CAAC;aACd;YACD,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG;gBACrC,GAAG,EAAE,KAAK;gBACV,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,IAAI;aACf,CAAC;YACF,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AA5HD,4CA4HC"}