{"version": 3, "file": "http.js", "sourceRoot": "", "sources": ["../src/http.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,2CAA6B;AAC7B,2CAAwC;AACxC,mCAAoC;AACpC,kDAAiC;AAEjC,yCAA2B;AAC3B,4CAA6C;AAC7C,+BAAgC;AAEhC,mCAAwC;AAExC,MAAM,KAAK,GAAG,IAAA,eAAY,EAAC,WAAW,CAAC,CAAC;AACxC,MAAM,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC;AASnD;;;;;;GAMG;AACH,MAAa,UAAU;IAKrB,YAAY,OAAkB;QAC5B,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;IAClD,CAAC;IAED;;;;;;;OAOG;IACI,YAAY,CAAC,IAAY,EAAE,IAAS,EAAE,SAAoB,EAAE,YAAwB,EAAE;QAC3F,MAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;QAErC,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC3B,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACrC,MAAM,OAAO,GAAa;YACxB,YAAY,EAAE,YAAY,GAAG,OAAO;YACpC,QAAQ,EAAE,0EAA0E;YACpF,iBAAiB,EAAE,MAAM;YACzB,gBAAgB,EAAE,OAAO;YACzB,GAAG,CAAC,SAAS,CAAC,OAAO,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,CAAC;YACtD,MAAM,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;SAC/C,CAAC;QACF,MAAM,YAAY,GAAG,CAAC,SAAS,CAAC,CAAC;QAEjC,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,YAAY,EAAE,GAAG,SAAS,CAAC;QACjE,MAAM,WAAW,GAAkB,YAAY,IAAI,EAAE,CAAC;QAEtD,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAChF,OAAO,CAAC,gBAAgB,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAC5D,OAAO,CAAC,cAAc,CAAC,GAAG,mCAAmC,CAAC;SAC/D;QAED,SAAS,GAAG,SAAS,IAAI,EAAE,CAAC;QAC5B,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE;YAC5B,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;SACjC;QAED,MAAM,OAAO,GAA2B;YACtC,GAAG,EAAE,IAAI,CAAC,IAAI;YACd,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,OAAO;YAChB,iBAAiB,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI;SAClC,CAAC;QACF,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;YACnB,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC;SAC/B;QACD,IAAI,SAAS,CAAC,SAAS,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YACjD,MAAM,KAAK,GAAG,IAAA,mBAAU,GAAE,CAAC;YAC3B,IAAI,MAAM,GAAG,IAAI,CAAC;YAClB,IAAI,OAAO,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;gBAClD,KAAK,MAAM,EAAE,IAAI,OAAO,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;oBACpD,IAAI,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;wBAC7B,MAAM,GAAG,EAAE,CAAC;qBACb;iBACF;aACF;YACD,MAAM,QAAQ,GAAG,IAAA,mBAAU,GAAE,CAAC;YAC9B,OAAO,CAAC,cAAc,CAAC,GAAG,yDAAyD,GAAG,KAAK,GAAG,sCAAsC,GAAG,QAAQ,CAAC;YAChJ,IAAI,MAAM,EAAE;gBACV,OAAO,CAAC,cAAc,CAAC,GAAG,OAAO,CAAC,cAAc,CAAC,GAAG,IAAI,GAAG,MAAM,CAAC;aACnE;YACD,MAAM,SAAS,GAAU,CAAC;oBACxB,cAAc,EAAE,qDAAqD;oBACrE,YAAY,EAAE,GAAG,GAAG,KAAK,GAAG,GAAG;oBAC/B,MAAM,EAAE,IAAI;iBACb,CAAC,CAAC;YAEH,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBACjC,SAAS,CAAC,IAAI,CAAC;oBACb,cAAc,EAAE,UAAU,CAAC,QAAQ;oBACnC,2BAA2B,EAAE,QAAQ;oBACrC,YAAY,EAAE,GAAG,GAAG,UAAU,CAAC,SAAS,GAAG,GAAG;oBAC9C,qBAAqB,EAAE,wBAAwB,GAAG,UAAU,CAAC,IAAI,GAAG,GAAG;oBACvE,MAAM,EAAE,UAAU,CAAC,IAAI;iBACxB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,OAAO,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,QAAQ,MAAM,CAAC,CAAC,CAAC;YAElD,IAAI,cAAc,GAAG,CAAC,CAAC;YACvB,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBACzB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;oBAChC,IAAI,GAAG,KAAK,MAAM,EAAE;wBAClB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;qBAC5D;gBACH,CAAC,CAAC,CAAC;gBACH,OAAO,CAAC,IAAI,CAAC,IAAI,CACf,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EACnB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EACtB,MAAM,CAAC,IAAI,CAAC,SAAS,QAAQ,GAC3B,cAAc,KAAK,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EACnD,MAAM,CAAC,CACR,CAAC;gBACF,cAAc,EAAE,CAAC;YACnB,CAAC,CAAC,CAAC;YACH,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SAC5C;aAAM;YACL,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;SACrB;QAED,IAAI,SAAS,CAAC,SAAS,EAAE;YACvB,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;YAC1B,OAAO,CAAC,IAAI,GAAG,IAAA,eAAQ,EAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACtC,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,GAAG,cAAc,CAAC;YACpD,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,GAAG,MAAM,CAAC;SAC9C;QAED,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE;YAC/B,IAAI,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;gBACrC,KAAK,MAAM,MAAM,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE;oBACpC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC;iBACjD;aACF;iBAAM;gBACL,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;aACjC;SACF;QACD,KAAK,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;QACnC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;;;OAMG;IACI,cAAc,CAAC,GAAqB,EAAE,GAAsB,EAAE,IAAS;QAC5E,KAAK,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC;QACtC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,6EAA6E;YAC7E,MAAM,KAAK,GAAG,iEAAiE,CAAC;YAChF,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC/D,IAAI,KAAK,EAAE;gBACT,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;aACjB;SACF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,OAAO,CACZ,IAAY,EACZ,IAAS,EACT,QAAoD,EACpD,SAAoB,EACpB,SAAsB,EACtB,MAAO;QAEP,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;QACpE,IAAI,GAAqB,CAAC;QAC1B,IAAI,SAAS,KAAK,SAAS,IAAI,SAAS,CAAC,IAAI,EAAE;YAC7C,MAAM,OAAO,GAAG,IAAA,uBAAU,EAAC;gBACzB,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,WAAW,EAAE,SAAS,CAAC,WAAW,IAAI,EAAE;gBACxC,MAAM,EAAE,SAAS,CAAC,MAAM,IAAI,EAAE;aAC/B,CAAC,CAAC;YACH,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;SACxB;aAAM;YACL,IAAI,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE;gBACxC,OAAO,CAAC,YAAY,GAAG,aAAa,CAAC;gBACrC,OAAO,CAAC,gBAAgB,GAAG,QAAQ,CAAC;aACrC;YACD,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;SAC9B;QACD,MAAM,KAAK,GAAG,IAAI,CAAC;QACnB,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;YAEf,MAAM,UAAU,GAAG,CAAC,IAAa,EAAE,EAAE;gBACnC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC3D,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC9B,OAAO,GAAG,CAAC;YACb,CAAC,CAAC;YAEF,IAAI,KAAK,CAAC,OAAO,CAAC,uBAAuB,EAAE;gBACzC,MAAM,eAAe,GAAG,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC;gBACnI,IAAI,eAAe,EAAE;oBACnB,IAAI,QAAQ,CAAC;oBACb,MAAM,iBAAiB,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC;oBACtE,IAAI,iBAAiB,EAAE;wBACrB,QAAQ,GAAG,iBAAiB,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;qBACzD;oBACD,IAAI,CAAC,QAAQ,EAAE;wBACb,OAAO,QAAQ,CAAC,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC,CAAC;qBAClE;oBACD,OAAO,IAAA,qBAAa,EAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,iBAAiB,EAAE,EAAE;wBAClE,IAAI,GAAG,EAAE;4BACP,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;yBACtB;wBACD,kCAAkC;wBAClC,MAAM,SAAS,GAAG,iBAAiB,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;wBAClD,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;4BACjC,OAAO,QAAQ,CAAC,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC,CAAC;yBAC/D;wBACA,GAAW,CAAC,uBAAuB,GAAG,iBAAiB,CAAC;wBACzD,OAAO,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;oBACrD,CAAC,CAAC,CAAC;iBACJ;qBAAM;oBACL,OAAO,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;iBAC9C;aACF;iBAAM;gBACL,OAAO,UAAU,EAAE,CAAC;aACrB;QACH,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE;YACT,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QACH,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,aAAa,CAAC,IAAY,EAAE,IAAS,EAAE,SAAoB,EAAE,SAAsB,EAAE,MAAO;QACjG,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;QACpE,OAAO,CAAC,YAAY,GAAG,QAAQ,CAAC;QAChC,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;YAC9C,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;YACnD,OAAO,GAAG,CAAC;QACb,CAAC,CAAC,CAAC;QACH,OAAO,GAAG,CAAC;IACb,CAAC;CACF;AApOD,gCAoOC"}