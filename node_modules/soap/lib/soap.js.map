{"version": 3, "file": "soap.js", "sourceRoot": "", "sources": ["../src/soap.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,kDAAiC;AACjC,qCAAkC;AAClC,sDAAwC;AACxC,qCAA8C;AAE9C,mCAA6C;AAC7C,iCAAyC;AAEzC,MAAM,KAAK,GAAG,IAAA,eAAY,EAAC,gBAAgB,CAAC,CAAC;AAEhC,QAAA,QAAQ,GAAG,SAAS,CAAC;AAClC,mCAAkC;AAAzB,gGAAA,MAAM,OAAA;AACf,+BAAoC;AAA3B,kGAAA,UAAU,OAAA;AACnB,uCAA+L;AAAtL,6GAAA,iBAAiB,OAAA;AAAE,0GAAA,cAAc,OAAA;AAAE,6GAAA,iBAAiB,OAAA;AAAE,gHAAA,oBAAoB,OAAA;AAAE,wGAAA,YAAY,OAAA;AAAE,sGAAA,UAAU,OAAA;AAAE,0GAAA,cAAc,OAAA;AAAE,8GAAA,kBAAkB,OAAA;AAAE,mHAAA,uBAAuB,OAAA;AAC1K,mCAAkC;AAAzB,gGAAA,MAAM,OAAA;AACf,iCAAyC;AAAhC,uGAAA,cAAc,OAAA;AACvB,0CAAwB;AACxB,+BAA8B;AAArB,4FAAA,IAAI,OAAA;AAIb,SAAS,YAAY,CAAC,GAAW,EAAE,KAAiB,EAAE,IAA+B,EAAE,QAAsB;IAC3G,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;QACnB,IAAI,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;YACnB,IAAI,GAAG,EAAE;gBACP,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;aACtB;YACD,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YACvB,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;KACJ;SAAM;QACL,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE;YACpB,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;KACJ;AACH,CAAC;AAED,SAAS,YAAY,CAAC,GAAW,EAAE,OAAiB,EAAE,QAAsB;IAC1E,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;QACjC,QAAQ,GAAG,OAAO,CAAC;QACnB,OAAO,GAAG,EAAE,CAAC;KACd;IACD,MAAM,QAAQ,GAAG,CAAC,QAAsB,EAAE,EAAE;QAC1C,IAAA,gBAAS,EAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IACpC,CAAC,CAAC;IAEF,IAAI,OAAO,CAAC,YAAY,KAAK,IAAI,EAAE;QACjC,QAAQ,CAAC,QAAQ,CAAC,CAAC;KACpB;SAAM;QACL,IAAI,KAAiB,CAAC;QACtB,IAAI,OAAO,CAAC,SAAS,EAAE;YACrB,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC;SAC3B;aAAM;YACL,KAAK,GAAG,0BAAkB,CAAC;SAC5B;QACD,YAAY,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;KAC9C;AACH,CAAC;AAMD,SAAgB,YAAY,CAAC,GAAW,EAAE,EAAmC,EAAE,EAAkC,EAAE,EAAW;IAC5H,IAAI,QAAQ,GAAW,EAAE,CAAC;IAC1B,IAAI,QAA8B,CAAC;IACnC,IAAI,OAAiB,CAAC;IACtB,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE;QAC5B,QAAQ,GAAG,EAAE,CAAC;QACd,QAAQ,GAAG,EAAY,CAAC;QACxB,OAAO,GAAG,EAAE,CAAC;KACd;SAAM,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE;QACnC,OAAO,GAAG,EAAE,CAAC;QACb,QAAQ,GAAG,EAAE,CAAC;QACd,QAAQ,GAAG,EAAE,CAAC;KACf;IACD,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,QAAQ,CAAC;IACxC,YAAY,CAAC,GAAG,EAAE,OAAO,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;QACvC,QAAQ,CAAC,GAAG,EAAE,IAAI,IAAI,IAAI,eAAM,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;AACL,CAAC;AAjBD,oCAiBC;AAED,SAAgB,iBAAiB,CAAC,GAAW,EAAE,OAAkB,EAAE,QAAiB;IAClF,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;QAClC,OAAO,GAAG,EAAE,CAAC;KACd;IACD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,YAAY,CAAC,GAAG,EAAE,OAAO,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;YACzC,IAAI,GAAG,EAAE;gBACP,MAAM,CAAC,GAAG,CAAC,CAAC;aACb;YACD,OAAO,CAAC,MAAM,CAAC,CAAC;QAClB,CAAC,EAAE,QAAQ,CAAC,CAAC;IACf,CAAC,CAAC,CAAC;AACL,CAAC;AAZD,8CAYC;AAID,SAAgB,MAAM,CAAC,MAAkB,EAAE,EAAoC,EAAE,QAAoB,EAAE,GAAY,EAAE,QAAuC;IAC1J,IAAI,OAAuB,CAAC;IAC5B,IAAI,IAAqB,CAAC;IAC1B,IAAI,GAAG,GAAG,EAAE,CAAC;IAEb,IAAI,OAAO,EAAE,KAAK,QAAQ,IAAI,CAAC,CAAC,EAAE,YAAY,MAAM,CAAC,EAAE;QACrD,gBAAgB;QAChB,kBAAkB;QAClB,OAAO,GAAG,EAAE,CAAC;QACb,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACpB,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAC5B,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;QAClB,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;KACnB;SAAM;QACL,aAAa;QACb,+BAA+B;QAC/B,IAAI,GAAG,EAAE,CAAC;QACV,OAAO,GAAG;YACR,IAAI,EAAE,EAAE;YACR,QAAQ,EAAE,QAAQ;YAClB,QAAQ,EAAE,QAAQ;SACnB,CAAC;KACH;IAED,MAAM,IAAI,GAAG,IAAI,WAAI,CAAC,GAAG,IAAI,QAAQ,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;IACrD,OAAO,IAAI,eAAM,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC3D,CAAC;AA1BD,wBA0BC"}