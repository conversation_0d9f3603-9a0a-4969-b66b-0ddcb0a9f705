{"version": 3, "file": "WSSecurity.js", "sourceRoot": "", "sources": ["../../src/security/WSSecurity.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AACA,+CAAiC;AAEjC,oCAAqD;AAErD,MAAM,kBAAkB,GAAG,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;AAY9D,MAAa,UAAU;IAWrB,YAAY,QAAgB,EAAE,QAAgB,EAAE,OAAqC;QACnF,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAC3B,+JAA+J;QAC/J,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAC/B,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC;YACxD,OAAO,GAAG,EAAE,CAAC;SACd;aAAM;YACL,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,cAAc,CAAC;SACnF;QAED,IAAI,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE;YACzD,IAAI,CAAC,aAAa,GAAG,cAAc,CAAC;SACrC;QAED,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,IAAI,OAAO,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC;QACvH,uBAAuB;QACvB,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,EAAE;YAC5B,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC;SACrC;QACD,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,eAAe,IAAI,OAAO,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC;QACnI,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI,EAAE;YACzB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;SAC7B;QACD,IAAI,OAAO,CAAC,cAAc,IAAI,IAAI,EAAE;YAClC,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC;SACjD;QACD,IAAI,OAAO,CAAC,WAAW,EAAE;YACvB,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,WAAW,CAAC;SACzC;IACH,CAAC;IAEM,KAAK;QACV,gDAAgD;QAChD,SAAS,OAAO,CAAC,CAAC;YAChB,SAAS,GAAG,CAAC,CAAC;gBACZ,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC;YACD,OAAO,CAAC,CAAC,cAAc,EAAE,GAAG,GAAG;kBAC3B,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG;kBAC9B,GAAG,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,GAAG;kBACzB,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG;kBAC1B,GAAG,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,GAAG;kBAC5B,GAAG,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,GAAG,CAAC;QACnC,CAAC;QACD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;QAC7B,IAAI,YAAY,GAAG,EAAE,CAAC;QACtB,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YAChE,YAAY,GAAG,mCAAmC,GAAG,OAAO,GAAG,IAAI;gBACjE,eAAe,GAAG,OAAO,GAAG,gBAAgB;gBAC5C,eAAe,GAAG,OAAO,GAAG,gBAAgB;gBAC5C,kBAAkB,CAAC;SACtB;QAED,IAAI,QAAQ,CAAC;QACb,IAAI,KAAK,CAAC;QACV,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,aAAa,KAAK,cAAc,EAAE;YAC3D,+CAA+C;YAC/C,MAAM,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACxC,KAAK,CAAC,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;YACtC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;SAChC;QACD,IAAI,IAAI,CAAC,aAAa,KAAK,cAAc,EAAE;YACzC,QAAQ,GAAG,wHAAwH,GAAG,IAAA,iBAAS,EAAC,IAAI,CAAC,SAAS,CAAC,GAAG,kBAAkB,CAAC;YACrL,IAAI,KAAK,EAAE;gBACT,QAAQ,IAAI,4HAA4H,GAAG,KAAK,GAAG,eAAe,CAAC;aACpK;SACF;aAAM;YACL;sCAC0B;YAC1B,QAAQ,GAAG,0HAA0H,GAAG,IAAA,sBAAc,EAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,kBAAkB;gBACzM,4HAA4H,GAAG,KAAK,GAAG,eAAe,CAAC;SAC1J;QAED,OAAO,iBAAiB,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,WAAW,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;YAC5F,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,sBAAsB,CAAC,CAAC,CAAC,EAAE,CAAC;YACxE,gMAAgM;YAChM,YAAY;YACZ,2IAA2I,GAAG,OAAO,GAAG,IAAI;YAC5J,iBAAiB,GAAG,IAAA,iBAAS,EAAC,IAAI,CAAC,SAAS,CAAC,GAAG,kBAAkB;YAClE,QAAQ;YACR,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,eAAe,GAAG,OAAO,GAAG,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC;YAC3E,uBAAuB;YACvB,kBAAkB,CAAC;IACvB,CAAC;CACF;AApGD,gCAoGC"}