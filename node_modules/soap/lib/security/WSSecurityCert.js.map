{"version": 3, "file": "WSSecurityCert.js", "sourceRoot": "", "sources": ["../../src/security/WSSecurityCert.ts"], "names": [], "mappings": ";;;AAAA,mCAAoC;AACpC,2CAAuC;AAGvC,SAAS,UAAU,CAAC,IAAU,EAAE,OAAe;IAC7C,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,OAAO,GAAG,KAAK,CAAC,CAAC;AACpD,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAU;IACnC,OAAO,IAAI,CAAC,cAAc,EAAE,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;QACnF,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;QACtF,CAAC,GAAG,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AAChG,CAAC;AAED,SAAS,eAAe;IACtB,OAAO,iBAAiB,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;AACvC,CAAC;AAED,SAAS,eAAe;IACtB,OAAO,iBAAiB,CAAC,UAAU,CAAC,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AACvD,CAAC;AAED,SAAS,SAAS,CAAC,GAAW,EAAE,GAAW,EAAE,GAAW;IACtD,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC3D,CAAC;AAED,SAAS,UAAU;IACjB,OAAO,IAAA,mBAAU,GAAE,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACzC,CAAC;AAED,SAAS,8BAA8B,CAAC,UAAiB,EAAE,SAAiB;IAC1E,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE;QAC5B,IAAI,GAAG,CAAC,KAAK,KAAK,oBAAoB,EAAE;YACtC,GAAG,CAAC,KAAK,GAAG,SAAS,CAAC;SACvB;KACF;AACH,CAAC;AAED,MAAM,YAAY,GAAG,wCAAwC,CAAC;AAC9D,MAAM,oBAAoB,GAAG,eAAe,CAAC;AAmB7C,MAAa,cAAc;IAYzB,YAAY,UAAe,EAAE,YAAiB,EAAE,QAAa,EAAE,UAAkC,EAAE;QAT3F,kBAAa,GAAsB,EAAE,CAAC;QAMtC,yBAAoB,GAAa,EAAE,CAAC;QACpC,iCAA4B,GAAa,EAAE,CAAC;QAGlD,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC,QAAQ,EAAE;aACxC,OAAO,CAAC,6BAA6B,EAAE,EAAE,CAAC;aAC1C,OAAO,CAAC,2BAA2B,EAAE,EAAE,CAAC;aACxC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;QAEjC,IAAI,CAAC,MAAM,GAAG,IAAI,sBAAS,CAAC;YAC1B,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,MAAM;YACtC,kBAAkB,EAAE,OAAO,EAAE,kBAAkB;SAChD,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,IAAI,yCAAyC,CAAC;QACnG,IAAI,OAAO,CAAC,kBAAkB,KAAK,mDAAmD,EAAE;YACtF,IAAI,CAAC,MAAM,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;YAC5D,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;gBACvB,KAAK,EAAE,oBAAoB;gBAC3B,UAAU,EAAE,CAAC,yCAAyC,CAAC;gBACvD,eAAe,EAAE,yCAAyC;aAC3D,CAAC,CAAC;SACJ;QAED,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE;YAC/B,IAAI,CAAC,MAAM,CAAC,kBAAkB,GAAG,mDAAmD,CAAC;SACtF;QAED,IAAI,CAAC,MAAM,CAAC,yBAAyB,GAAG,yCAAyC,CAAC;QAElF,IAAI,OAAO,CAAC,oBAAoB,IAAI,OAAO,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3E,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,CAAC;SAC1D;QAED,IAAI,OAAO,CAAC,4BAA4B,IAAI,OAAO,CAAC,4BAA4B,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3F,IAAI,CAAC,4BAA4B,GAAG,OAAO,CAAC,4BAA4B,CAAC;SAC1E;QAED,IAAI,OAAO,CAAC,aAAa,EAAE;YACzB,MAAM,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;YAClC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE;gBACxC,IAAI,CAAC,aAAa,CAAC,gBAAgB,GAAG,EAAE,CAAC;aAC1C;YACD,IAAI,IAAI,CAAC,aAAa,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,IAAI,EAAE;gBACpF,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,IAAI,GAAG,GAAG,YAAY,6CAA6C,CAAC;aACzG;SACF;aAAM;YACL,IAAI,CAAC,aAAa,GAAG,EAAE,gBAAgB,EAAE,EAAE,IAAI,EAAE,GAAG,YAAY,6CAA6C,EAAE,EAAE,CAAC;SACnH;QAED,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG;YACvB,GAAG,EAAE,UAAU;YACf,UAAU,EAAE,QAAQ;SACrB,CAAC;QACF,IAAI,CAAC,MAAM,GAAG,QAAQ,UAAU,EAAE,EAAE,CAAC;QACrC,IAAI,CAAC,YAAY,GAAG,OAAO,OAAO,CAAC,YAAY,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC;QAChG,IAAI,CAAC,wBAAwB,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,wBAAwB;YAChH,CAAC,CAAC,CAAC,uDAAuD,EAAE,yCAAyC,CAAC,CAAC;QAEzG,IAAI,CAAC,MAAM,CAAC,iBAAiB,GAAG,CAAC,GAAG,EAAE,EAAE;YACtC,OAAO,+BAA+B;gBACpC,yBAAyB,IAAI,CAAC,MAAM,gBAAgB,YAAY,oDAAoD;gBACpH,gCAAgC,CAAC;QACrC,CAAC,CAAC;IACJ,CAAC;IAEM,WAAW,CAAC,GAAW,EAAE,WAAmB;QACjD,IAAI,CAAC,OAAO,GAAG,eAAe,EAAE,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,eAAe,EAAE,CAAC;QAEjC,IAAI,YAAY,GAAG,EAAE,CAAC;QACtB,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,YAAY;gBACV,qBAAqB,YAAY,wDAAwD;oBACzF,YAAY,IAAI,CAAC,OAAO,YAAY;oBACpC,YAAY,IAAI,CAAC,OAAO,YAAY;oBACpC,cAAc,CAAC;SAClB;QAED,MAAM,mBAAmB,GAAG,4BAA4B;YACtD,iBAAiB,YAAY,4DAA4D;YACzF,cAAc,YAAY,mDAAmD;YAC7E,WAAW,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,YAAY,6BAA6B;YACzE,YAAY,CAAC;QAEf,IAAI,UAAkB,CAAC;QACvB,MAAM,MAAM,GAAG,eAAe,YAAY,8CAA8C,CAAC;QACzF,MAAM,UAAU,GAAG,cAAc,YAAY,+CAA+C,CAAC;QAC7F,MAAM,mBAAmB,GAAG,GAAG,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAC5D,IAAI,mBAAmB,GAAG,CAAC,CAAC,EAAE;YAC5B,MAAM,oBAAoB,GAAG,0BAA0B,CAAC;YACxD,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;YAC9C,IAAI,sBAAsB,GAAG,EAAE,CAAC;YAChC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,WAAW,mBAAmB,CAAC,EAAE;gBAC1D,sBAAsB,IAAI,GAAG,WAAW,sBAAsB,CAAC;aAChE;YACD,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;gBAChE,sBAAsB,IAAI,GAAG,MAAM,GAAG,CAAC;aACxC;YACD,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;gBACpE,sBAAsB,IAAI,GAAG,UAAU,GAAG,CAAC;aAC5C;YACD,MAAM,YAAY,GAAG,iBAAiB,CAAC;YACvC,MAAM,qBAAqB,GAAG,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACxD,GAAG,GAAG,SAAS,CAAC,mBAAmB,EAAE,GAAG,EAAE,mBAAmB,CAAC,CAAC;YAC/D,UAAU,GAAG,SAAS,CAAC,sBAAsB,EAAE,GAAG,EAAE,qBAAqB,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;SAClG;aAAM;YACL,MAAM,SAAS,GACb,kBAAkB,MAAM,GAAG;gBAC3B,GAAG,UAAU,GAAG;gBAChB,GAAG,WAAW,sBAAsB;gBACpC,mBAAmB;gBACnB,kBAAkB,CAAC;YAErB,UAAU,GAAG,SAAS,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC,OAAO,CAAC,KAAK,WAAW,UAAU,CAAC,CAAC,CAAC;SACjF;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC;QAEjD,MAAM,SAAS,GAAG,gBAAgB,WAAW,SAAS,CAAC;QACvD,8BAA8B,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QAElE,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,GAAuB,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;YACzK,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC;SACxH;QAED,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC5C,MAAM,KAAK,GAAG,gBAAgB,IAAI,IAAI,CAAC;YACvC,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,GAAuB,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;gBACrK,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC;aAClH;SACF;QAED,MAAM,cAAc,GAAG,2DAA2D,CAAC;QACnF,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,KAAK,WAAW,CAAC,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,GAAuB,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,KAAK,cAAc,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;YAC1M,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC;SAC3H;QAED,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAE7D,MAAM,kBAAkB,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC;QAC/D,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;QACnD,OAAO,SAAS,CAAC,YAAY,EAAE,kBAAkB,EAAE,kBAAkB,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC;IACrG,CAAC;CACF;AA1JD,wCA0JC"}