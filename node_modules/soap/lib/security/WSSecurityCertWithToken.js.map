{"version": 3, "file": "WSSecurityCertWithToken.js", "sourceRoot": "", "sources": ["../../src/security/WSSecurityCertWithToken.ts"], "names": [], "mappings": ";;;AAAA,mCAAoC;AACpC,2CAAuC;AAIvC,SAAS,UAAU,CAAC,IAAU,EAAE,OAAe;IAC7C,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,OAAO,GAAG,KAAK,CAAC,CAAC;AACpD,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAU;IACnC,OAAO,IAAI,CAAC,cAAc,EAAE,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;QACnF,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;QACtF,CAAC,GAAG,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AAChG,CAAC;AAED,SAAS,eAAe;IACtB,OAAO,iBAAiB,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;AACvC,CAAC;AAED,SAAS,eAAe;IACtB,OAAO,iBAAiB,CAAC,UAAU,CAAC,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AACvD,CAAC;AAED,SAAS,SAAS,CAAC,GAAW,EAAE,GAAW,EAAE,GAAW;IACtD,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC3D,CAAC;AAED,SAAS,UAAU;IACjB,OAAO,IAAA,mBAAU,GAAE,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACzC,CAAC;AAED,SAAS,8BAA8B,CAAC,UAAiB,EAAE,SAAiB;IAC1E,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE;QAC5B,IAAI,GAAG,CAAC,KAAK,KAAK,oBAAoB,EAAE;YACtC,GAAG,CAAC,KAAK,GAAG,SAAS,CAAC;SACvB;KACF;AACH,CAAC;AAED,MAAM,YAAY,GAAG,wCAAwC,CAAC;AAC9D,MAAM,oBAAoB,GAAG,eAAe,CAAC;AAE7C,MAAa,uBAAuB;IAalC,YAAY,KAA4I;QAVhJ,kBAAa,GAAsB,EAAE,CAAC;QAMtC,yBAAoB,GAAa,EAAE,CAAC;QAK1C,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,SAAS,CAAC,QAAQ,EAAE;aAC3C,OAAO,CAAC,6BAA6B,EAAE,EAAE,CAAC;aAC1C,OAAO,CAAC,2BAA2B,EAAE,EAAE,CAAC;aACxC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;QACjC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;QAC/B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;QAE/B,IAAI,CAAC,MAAM,GAAG,IAAI,sBAAS,EAAE,CAAC;QAC9B,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC;QACjC,IAAI,IAAI,CAAC,kBAAkB,KAAK,mDAAmD,EAAE;YACnF,IAAI,CAAC,MAAM,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;YACzD,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;gBACvB,KAAK,EAAE,oBAAoB;gBAC3B,UAAU,EAAE,CAAC,yCAAyC,CAAC;gBACvD,eAAe,EAAE,yCAAyC;aAC3D,CAAC,CAAC;SACJ;QAED,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC5B,IAAI,CAAC,MAAM,CAAC,kBAAkB,GAAG,mDAAmD,CAAC;SACtF;QAED,IAAI,CAAC,MAAM,CAAC,yBAAyB,GAAG,yCAAyC,CAAC;QAElF,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;YACrE,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;SACvD;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,MAAM,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC;YACxC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE;gBACxC,IAAI,CAAC,aAAa,CAAC,gBAAgB,GAAG,EAAE,CAAC;aAC1C;YACD,IAAI,IAAI,CAAC,aAAa,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,IAAI,EAAE;gBACpF,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,IAAI,GAAG,GAAG,YAAY,6CAA6C,CAAC;aACzG;SACF;aAAM;YACL,IAAI,CAAC,aAAa,GAAG,EAAE,gBAAgB,EAAE,EAAE,IAAI,EAAE,GAAG,YAAY,6CAA6C,EAAE,EAAE,CAAC;SACnH;QAED,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG;YACvB,GAAG,EAAE,KAAK,CAAC,UAAU;YACrB,UAAU,EAAE,KAAK,CAAC,WAAW;SAC9B,CAAC;QAEF,IAAI,CAAC,MAAM,GAAG,QAAQ,UAAU,EAAE,EAAE,CAAC;QACrC,IAAI,CAAC,YAAY,GAAG,OAAO,IAAI,CAAC,YAAY,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;QAC1F,IAAI,CAAC,wBAAwB,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,wBAAwB;YAC1G,CAAC,CAAC,CAAC,uDAAuD,EAAE,yCAAyC,CAAC,CAAC;QAEzG,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,GAAG,EAAE,EAAE;YAC/B,OAAO,+BAA+B;gBACpC,yBAAyB,IAAI,CAAC,MAAM,gBAAgB,YAAY,oDAAoD;gBACpH,gCAAgC,CAAC;QACrC,CAAC,CAAC;IACJ,CAAC;IAEM,WAAW,CAAC,GAAG,EAAE,WAAW;QACjC,IAAI,CAAC,OAAO,GAAG,eAAe,EAAE,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,eAAe,EAAE,CAAC;QAEjC,IAAI,YAAY,GAAG,EAAE,CAAC;QACtB,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,YAAY;gBACV,qBAAqB,YAAY,wDAAwD;oBACzF,YAAY,IAAI,CAAC,OAAO,YAAY;oBACpC,YAAY,IAAI,CAAC,OAAO,YAAY;oBACpC,cAAc,CAAC;SAClB;QACD,IAAI,aAAa,GAAG,EAAE,CAAC;QACvB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,aAAa,GAAG,6CAA6C,IAAI,CAAC,OAAO,mGAAmG;gBAC1K,kBAAkB,IAAI,CAAC,QAAQ,mBAAmB;gBAClD,kBAAkB,IAAI,CAAC,QAAQ,mBAAmB;gBAClD,uBAAuB,CAAC;SAC3B;QACD,MAAM,SAAS,GACb,8BAA8B,YAAY,+CAA+C;YACzF,cAAc,YAAY,gDAAgD;YAC1E,GAAG,WAAW,sBAAsB;YACpC,4BAA4B;YAC5B,iBAAiB,YAAY,4DAA4D;YACzF,cAAc,YAAY,mDAAmD;YAC7E,WAAW,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,YAAY,6BAA6B;YACzE,aAAa;YACb,YAAY;YACZ,kBAAkB,CAAC;QAErB,MAAM,UAAU,GAAG,SAAS,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC,OAAO,CAAC,KAAK,WAAW,UAAU,CAAC,CAAC,CAAC;QAEtF,MAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC;QAEjD,MAAM,SAAS,GAAG,gBAAgB,WAAW,SAAS,CAAC;QACvD,8BAA8B,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QAElE,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;YACnF,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,EAAE,yCAAyC,EAAE,CAAC,CAAC;SACpI;QAED,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC5C,MAAM,KAAK,GAAG,gBAAgB,IAAI,IAAI,CAAC;YACvC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;gBAC/E,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,EAAE,yCAAyC,EAAE,CAAC,CAAC;aAChI;SACF;QAED,MAAM,cAAc,GAAG,2DAA2D,CAAC;QACnF,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,KAAK,cAAc,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;YAC7G,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,EAAE,yCAAyC,EAAE,CAAC,CAAC;SACzI;QAED,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAE7D,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,EAAE,UAAU,EAAE,UAAU,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC;IACtG,CAAC;CACF;AAnID,0DAmIC"}