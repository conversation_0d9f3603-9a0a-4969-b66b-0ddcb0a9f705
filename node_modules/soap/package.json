{"name": "soap", "version": "1.1.12", "description": "A minimal node SOAP client", "engines": {"node": ">=14.17.0"}, "author": "<PERSON><PERSON> <<EMAIL>>", "dependencies": {"axios": "^1.9.0", "axios-ntlm": "^1.4.4", "debug": "^4.4.1", "formidable": "^3.5.4", "get-stream": "^6.0.1", "lodash": "^4.17.21", "sax": "^1.4.1", "strip-bom": "^3.0.0", "whatwg-mimetype": "4.0.0", "xml-crypto": "^6.1.2"}, "repository": {"type": "git", "url": "https://github.com/vpulim/node-soap.git"}, "main": "./index.js", "types": "./lib/soap.d.ts", "directories": {"lib": "./lib", "test": "./test"}, "scripts": {"prepare": "npm run build", "build": "tsc -p .", "clean": "rm -rf lib", "watch": "tsc -w -p .", "lint": "tslint -p tsconfig.json", "toc": "./node_modules/.bin/doctoc Readme.md --github --maxlevel 3", "cover": "nyc --extension=.ts --reporter=lcov --reporter=html --reporter=text mocha --timeout 15000 --exit test/*-test.js test/security/*.js", "coveralls": "cat ./coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js -v", "docs": "typedoc --out docs", "test": "mocha --timeout 15000 --bail --exit test/*-test.js test/security/*.js"}, "keywords": ["soap"], "license": "MIT", "devDependencies": {"@types/debug": "^4.1.12", "@types/formidable": "^3.4.5", "@types/lodash": "^4.17.13", "@types/node": "^14.0.0", "@types/sax": "^1.2.7", "@types/whatwg-mimetype": "^3.0.2", "body-parser": "^1.20.3", "colors": "^1.4.0", "diff": "^7.0.0", "doctoc": "^2.2.1", "duplexer": "~0.1.2", "express": "^4.21.1", "finalhandler": "^2.1.0", "glob": "^11.0.1", "mocha": "^11.1.0", "nyc": "^17.1.0", "readable-stream": "^4.7.0", "semver": "^7.7.1", "serve-static": "^1.15.0", "should": "^13.2.3", "sinon": "^20.0.0", "source-map-support": "^0.5.21", "timekeeper": "^2.3.1", "tslint": "^5.18.0", "typedoc": "^0.23.10", "typescript": "^4.7.4"}}