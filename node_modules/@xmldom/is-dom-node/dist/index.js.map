{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;AAAA,IAAY,SAUX;AAVD,WAAY,SAAS;IACnB,yDAAgB,CAAA;IAChB,6DAAkB,CAAA;IAClB,mDAAa,CAAA;IACb,qEAAsB,CAAA;IACtB,uFAA+B,CAAA;IAC/B,yDAAgB,CAAA;IAChB,2DAAiB,CAAA;IACjB,sEAAuB,CAAA;IACvB,8EAA2B,CAAA;AAC7B,CAAC,EAVW,SAAS,yBAAT,SAAS,QAUpB;AAED,8DAA8D;AAC9D,SAAgB,UAAU,CAAC,KAAU;IACnC,OAAO,CACL,KAAK;QACL,OAAO,KAAK,KAAK,QAAQ;QACzB,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC;QAChC,KAAK,CAAC,QAAQ,IAAI,CAAC;QACnB,KAAK,CAAC,QAAQ,IAAI,EAAE;QACpB,OAAO,KAAK,CAAC,QAAQ,KAAK,QAAQ;QAClC,OAAO,KAAK,CAAC,WAAW,KAAK,UAAU;QACvC,OAAO,KAAK,CAAC,WAAW,KAAK,UAAU,CACxC,CAAC;AACJ,CAAC;AAXD,gCAWC;AAED,SAAgB,gBAAgB,CAAC,KAAc;IAC7C,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;QACtB,MAAM,IAAI,KAAK,CAAC,8CAA8C,KAAK,EAAE,CAAC,CAAC;KACxE;AACH,CAAC;AAJD,4CAIC;AAED,SAAgB,cAAc,CAAC,KAAc;IAC3C,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AACzD,CAAC;AAFD,wCAEC;AAED,SAAgB,oBAAoB,CAAC,KAAc;IACjD,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;QAC1B,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;KACnD;AACH,CAAC;AAJD,oDAIC;AAED,SAAS,YAAY,CAAC,IAAY,EAAE,KAAc;IAChD,OAAO,UAAU,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC;AACtD,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAY,EAAE,KAAc;IACtD,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAC1C,CAAC,GAAG,EAAE,EAAE,CAAC,SAAS,CAAC,GAA6B,CAAC,KAAK,IAAI,CAC3D,CAAC;IACF,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;QAC9B,MAAM,IAAI,KAAK,CAAC,wBAAwB,QAAQ,EAAE,CAAC,CAAC;KACrD;AACH,CAAC;AAEM,MAAM,aAAa,GAAG,CAAC,KAAc,EAAoB,EAAE,CAChE,YAAY,CAAC,SAAS,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;AADjC,QAAA,aAAa,iBACoB;AACvC,MAAM,eAAe,GAAG,CAAC,KAAc,EAAiB,EAAE,CAC/D,YAAY,CAAC,SAAS,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;AADnC,QAAA,eAAe,mBACoB;AACzC,MAAM,UAAU,GAAG,CAAC,KAAc,EAAiB,EAAE,CAC1D,YAAY,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AAD9B,QAAA,UAAU,cACoB;AACpC,MAAM,kBAAkB,GAAG,CAAC,KAAc,EAAyB,EAAE,CAC1E,YAAY,CAAC,SAAS,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;AADvC,QAAA,kBAAkB,sBACqB;AAC7C,MAAM,2BAA2B,GAAG,CAAC,KAAc,EAAkC,EAAE,CAC5F,YAAY,CAAC,SAAS,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;AADhD,QAAA,2BAA2B,+BACqB;AACtD,MAAM,aAAa,GAAG,CAAC,KAAc,EAAoB,EAAE,CAChE,YAAY,CAAC,SAAS,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;AADjC,QAAA,aAAa,iBACoB;AACvC,MAAM,cAAc,GAAG,CAAC,KAAc,EAAqB,EAAE,CAClE,YAAY,CAAC,SAAS,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;AADlC,QAAA,cAAc,kBACoB;AACxC,MAAM,kBAAkB,GAAG,CAAC,KAAc,EAAyB,EAAE,CAC1E,YAAY,CAAC,SAAS,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;AADvC,QAAA,kBAAkB,sBACqB;AAC7C,MAAM,sBAAsB,GAAG,CAAC,KAAc,EAA6B,EAAE,CAClF,YAAY,CAAC,SAAS,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;AAD3C,QAAA,sBAAsB,0BACqB;AAEjD,MAAM,mBAAmB,GAAiD,CAC/E,KAAc,EACY,EAAE;IAC5B,kBAAkB,CAAC,SAAS,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;AACpD,CAAC,CAAC;AAJW,QAAA,mBAAmB,uBAI9B;AACK,MAAM,qBAAqB,GAA8C,CAAC,KAAK,EAAE,EAAE,CACxF,kBAAkB,CAAC,SAAS,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;AADzC,QAAA,qBAAqB,yBACoB;AAE/C,MAAM,gBAAgB,GAA8C,CAAC,KAAK,EAAE,EAAE,CACnF,kBAAkB,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AADpC,QAAA,gBAAgB,oBACoB;AAE1C,MAAM,wBAAwB,GAAsD,CACzF,KAAK,EACL,EAAE,CAAC,kBAAkB,CAAC,SAAS,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;AAFhD,QAAA,wBAAwB,4BAEwB;AAEtD,MAAM,iCAAiC,GAEA,CAAC,KAAK,EAAE,EAAE,CACtD,kBAAkB,CAAC,SAAS,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;AAHtD,QAAA,iCAAiC,qCAGqB;AAE5D,MAAM,mBAAmB,GAAiD,CAAC,KAAK,EAAE,EAAE,CACzF,kBAAkB,CAAC,SAAS,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;AADvC,QAAA,mBAAmB,uBACoB;AAE7C,MAAM,oBAAoB,GAAkD,CAAC,KAAK,EAAE,EAAE,CAC3F,kBAAkB,CAAC,SAAS,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;AADxC,QAAA,oBAAoB,wBACoB;AAE9C,MAAM,wBAAwB,GAAsD,CACzF,KAAK,EACL,EAAE,CAAC,kBAAkB,CAAC,SAAS,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;AAFhD,QAAA,wBAAwB,4BAEwB;AAEtD,MAAM,4BAA4B,GAA0D,CACjG,KAAK,EACL,EAAE,CAAC,kBAAkB,CAAC,SAAS,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;AAFpD,QAAA,4BAA4B,gCAEwB", "sourcesContent": ["export enum NodeTypes {\n  ELEMENT_NODE = 1,\n  ATTRIBUTE_NODE = 2,\n  TEXT_NODE = 3,\n  CDATA_SECTION_NODE = 4,\n  PROCESSING_INSTRUCTION_NODE = 7,\n  COMMENT_NODE = 8,\n  DOCUMENT_NODE = 9,\n  DOCUMENT_TYPE_NODE = 10,\n  DOCUMENT_FRAGMENT_NODE = 11,\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function isNodeLike(value: any): value is Node {\n  return (\n    value &&\n    typeof value === \"object\" &&\n    Number.isInteger(value.nodeType) &&\n    value.nodeType >= 1 &&\n    value.nodeType <= 11 &&\n    typeof value.nodeName === \"string\" &&\n    typeof value.appendChild === \"function\" &&\n    typeof value.removeChild === \"function\"\n  );\n}\n\nexport function assertIsNodeLike(value: unknown): asserts value is Node {\n  if (!isNodeLike(value)) {\n    throw new Error(`Value is not a Node-like object. Received: ${value}`);\n  }\n}\n\nexport function isArrayOfNodes(value: unknown): value is Node[] {\n  return Array.isArray(value) && value.every(isNodeLike);\n}\n\nexport function assertIsArrayOfNodes(value: unknown): asserts value is Node[] {\n  if (!isArrayOfNodes(value)) {\n    throw new Error(\"Value is not an array of Nodes\");\n  }\n}\n\nfunction isNodeOfType(type: number, value: unknown): value is Node {\n  return isNodeLike(value) && value.nodeType === type;\n}\n\nfunction assertIsNodeOfType(type: number, value: unknown): void {\n  const typeName = Object.keys(NodeTypes).find(\n    (key) => NodeTypes[key as keyof typeof NodeTypes] === type,\n  );\n  if (!isNodeOfType(type, value)) {\n    throw new Error(`Value is not of type ${typeName}`);\n  }\n}\n\nexport const isElementNode = (value: unknown): value is Element =>\n  isNodeOfType(NodeTypes.ELEMENT_NODE, value);\nexport const isAttributeNode = (value: unknown): value is Attr =>\n  isNodeOfType(NodeTypes.ATTRIBUTE_NODE, value);\nexport const isTextNode = (value: unknown): value is Text =>\n  isNodeOfType(NodeTypes.TEXT_NODE, value);\nexport const isCDATASectionNode = (value: unknown): value is CDATASection =>\n  isNodeOfType(NodeTypes.CDATA_SECTION_NODE, value);\nexport const isProcessingInstructionNode = (value: unknown): value is ProcessingInstruction =>\n  isNodeOfType(NodeTypes.PROCESSING_INSTRUCTION_NODE, value);\nexport const isCommentNode = (value: unknown): value is Comment =>\n  isNodeOfType(NodeTypes.COMMENT_NODE, value);\nexport const isDocumentNode = (value: unknown): value is Document =>\n  isNodeOfType(NodeTypes.DOCUMENT_NODE, value);\nexport const isDocumentTypeNode = (value: unknown): value is DocumentType =>\n  isNodeOfType(NodeTypes.DOCUMENT_TYPE_NODE, value);\nexport const isDocumentFragmentNode = (value: unknown): value is DocumentFragment =>\n  isNodeOfType(NodeTypes.DOCUMENT_FRAGMENT_NODE, value);\n\nexport const assertIsElementNode: (value: unknown) => asserts value is Element = (\n  value: unknown,\n): asserts value is Element => {\n  assertIsNodeOfType(NodeTypes.ELEMENT_NODE, value);\n};\nexport const assertIsAttributeNode: (value: unknown) => asserts value is Attr = (value) =>\n  assertIsNodeOfType(NodeTypes.ATTRIBUTE_NODE, value);\n\nexport const assertIsTextNode: (value: unknown) => asserts value is Text = (value) =>\n  assertIsNodeOfType(NodeTypes.TEXT_NODE, value);\n\nexport const assertIsCDATASectionNode: (value: unknown) => asserts value is CDATASection = (\n  value,\n) => assertIsNodeOfType(NodeTypes.CDATA_SECTION_NODE, value);\n\nexport const assertIsProcessingInstructionNode: (\n  value: unknown,\n) => asserts value is ProcessingInstruction = (value) =>\n  assertIsNodeOfType(NodeTypes.PROCESSING_INSTRUCTION_NODE, value);\n\nexport const assertIsCommentNode: (value: unknown) => asserts value is Comment = (value) =>\n  assertIsNodeOfType(NodeTypes.COMMENT_NODE, value);\n\nexport const assertIsDocumentNode: (value: unknown) => asserts value is Document = (value) =>\n  assertIsNodeOfType(NodeTypes.DOCUMENT_NODE, value);\n\nexport const assertIsDocumentTypeNode: (value: unknown) => asserts value is DocumentType = (\n  value,\n) => assertIsNodeOfType(NodeTypes.DOCUMENT_TYPE_NODE, value);\n\nexport const assertIsDocumentFragmentNode: (value: unknown) => asserts value is DocumentFragment = (\n  value,\n) => assertIsNodeOfType(NodeTypes.DOCUMENT_FRAGMENT_NODE, value);\n"]}