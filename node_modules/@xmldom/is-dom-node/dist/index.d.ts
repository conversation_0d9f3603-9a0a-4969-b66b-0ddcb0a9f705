export declare enum NodeTypes {
    ELEMENT_NODE = 1,
    ATTRIBUTE_NODE = 2,
    TEXT_NODE = 3,
    CDATA_SECTION_NODE = 4,
    PROCESSING_INSTRUCTION_NODE = 7,
    COMMENT_NODE = 8,
    DOCUMENT_NODE = 9,
    DOCUMENT_TYPE_NODE = 10,
    DOCUMENT_FRAGMENT_NODE = 11
}
export declare function isNodeLike(value: any): value is Node;
export declare function assertIsNodeLike(value: unknown): asserts value is Node;
export declare function isArrayOfNodes(value: unknown): value is Node[];
export declare function assertIsArrayOfNodes(value: unknown): asserts value is Node[];
export declare const isElementNode: (value: unknown) => value is Element;
export declare const isAttributeNode: (value: unknown) => value is Attr;
export declare const isTextNode: (value: unknown) => value is Text;
export declare const isCDATASectionNode: (value: unknown) => value is CDATASection;
export declare const isProcessingInstructionNode: (value: unknown) => value is ProcessingInstruction;
export declare const isCommentNode: (value: unknown) => value is Comment;
export declare const isDocumentNode: (value: unknown) => value is Document;
export declare const isDocumentTypeNode: (value: unknown) => value is DocumentType;
export declare const isDocumentFragmentNode: (value: unknown) => value is DocumentFragment;
export declare const assertIsElementNode: (value: unknown) => asserts value is Element;
export declare const assertIsAttributeNode: (value: unknown) => asserts value is Attr;
export declare const assertIsTextNode: (value: unknown) => asserts value is Text;
export declare const assertIsCDATASectionNode: (value: unknown) => asserts value is CDATASection;
export declare const assertIsProcessingInstructionNode: (value: unknown) => asserts value is ProcessingInstruction;
export declare const assertIsCommentNode: (value: unknown) => asserts value is Comment;
export declare const assertIsDocumentNode: (value: unknown) => asserts value is Document;
export declare const assertIsDocumentTypeNode: (value: unknown) => asserts value is DocumentType;
export declare const assertIsDocumentFragmentNode: (value: unknown) => asserts value is DocumentFragment;
