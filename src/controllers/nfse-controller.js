/**
 * Controller para operações de NFSe
 */

const FiorilliClient = require('../services/fiorilli-client');
const XmlBuilder = require('../services/xml-builder');
const logger = require('../utils/logger');
const { formatResponse, formatError } = require('../utils/response-formatter');

// Instâncias globais dos serviços
const fiorilliClient = new FiorilliClient();
const xmlBuilder = new XmlBuilder();

/**
 * Gerar NFSe individual
 */
async function gerarNfse(req, res) {
  try {
    logger.info('Iniciando geração de NFSe', {
      rpsNumero: req.body.Numero,
      prestadorCnpj: req.body.Prestador?.Cnpj
    });

    // Construir XML do RPS
    const xmlRps = xmlBuilder.buildGerarNfseXml(req.body);

    // Enviar para Fiorilli
    const resultado = await fiorilliClient.gerarNfse(xmlRps);

    logger.info('NFSe gerada com sucesso', {
      rpsNumero: req.body.Numero,
      resultado: resultado
    });

    res.json(formatResponse({
      message: 'NFSe gerada com sucesso',
      data: resultado,
      rpsOriginal: req.body
    }));

  } catch (error) {
    logger.error('Erro ao gerar NFSe', {
      error: error.message,
      stack: error.stack,
      rpsData: req.body
    });

    res.status(500).json(formatError(error, 'Erro ao gerar NFSe'));
  }
}

/**
 * Enviar lote de RPS
 */
async function enviarLoteRps(req, res) {
  try {
    const { lote } = req.body;

    logger.info('Iniciando envio de lote RPS', {
      numeroLote: lote.NumeroLote,
      quantidadeRps: lote.ListaRps?.length
    });

    // Validar quantidade de RPS no lote
    if (!lote.ListaRps || lote.ListaRps.length === 0) {
      return res.status(400).json(formatError(
        new Error('Lote deve conter pelo menos 1 RPS'),
        'Dados inválidos'
      ));
    }

    if (lote.ListaRps.length > 50) {
      return res.status(400).json(formatError(
        new Error('Lote não pode conter mais de 50 RPS'),
        'Dados inválidos'
      ));
    }

    // Construir XML do lote
    const xmlLote = xmlBuilder.buildRecepcionarLoteRpsXml(lote);

    // Enviar para Fiorilli
    const resultado = await fiorilliClient.recepcionarLoteRps(xmlLote);

    logger.info('Lote RPS enviado com sucesso', {
      numeroLote: lote.NumeroLote,
      resultado: resultado
    });

    res.json(formatResponse({
      message: 'Lote RPS enviado com sucesso',
      data: resultado,
      loteOriginal: lote
    }));

  } catch (error) {
    logger.error('Erro ao enviar lote RPS', {
      error: error.message,
      stack: error.stack,
      loteData: req.body
    });

    res.status(500).json(formatError(error, 'Erro ao enviar lote RPS'));
  }
}

/**
 * Enviar lote de RPS
 */
async function enviarLoteRps(req, res) {
  try {
    const { lote } = req.body;

    logger.info('Iniciando envio de lote RPS', {
      numeroLote: lote.NumeroLote,
      quantidadeRps: lote.ListaRps?.length
    });

    // Validar quantidade de RPS no lote
    if (!lote.ListaRps || lote.ListaRps.length === 0) {
      return res.status(400).json(formatError(
        new Error('Lote deve conter pelo menos 1 RPS'),
        'Dados inválidos'
      ));
    }

    if (lote.ListaRps.length > 50) {
      return res.status(400).json(formatError(
        new Error('Lote não pode conter mais de 50 RPS'),
        'Dados inválidos'
      ));
    }

    // Construir XML do lote
    const xmlLote = xmlBuilder.buildRecepcionarLoteRpsXml(lote);

    // Enviar para Fiorilli
    const resultado = await fiorilliClient.recepcionarLoteRps(xmlLote);

    logger.info('Lote RPS enviado com sucesso', {
      numeroLote: lote.NumeroLote,
      resultado: resultado
    });

    res.json(formatResponse({
      message: 'Lote RPS enviado com sucesso',
      data: resultado,
      loteOriginal: lote
    }));

  } catch (error) {
    logger.error('Erro ao enviar lote RPS', {
      error: error.message,
      stack: error.stack,
      loteData: req.body
    });

    res.status(500).json(formatError(error, 'Erro ao enviar lote RPS'));
  }
}

/**
 * Enviar lote de RPS síncrono
 */
async function enviarLoteRpsSincrono(req, res) {
  try {
    const { lote } = req.body;

    logger.info('Iniciando envio de lote RPS síncrono', {
      numeroLote: lote.NumeroLote,
      quantidadeRps: lote.ListaRps?.length
    });

    // Construir XML do lote
    const xmlLote = xmlBuilder.buildRecepcionarLoteRpsXml(lote);

    // Enviar para Fiorilli (método síncrono)
    const resultado = await fiorilliClient.recepcionarLoteRpsSincrono(xmlLote);

    logger.info('Lote RPS processado sincronamente', {
      numeroLote: lote.NumeroLote,
      resultado: resultado
    });

    res.json(formatResponse({
      message: 'Lote RPS processado com sucesso',
      data: resultado,
      loteOriginal: lote
    }));

  } catch (error) {
    logger.error('Erro ao processar lote RPS síncrono', {
      error: error.message,
      stack: error.stack,
      loteData: req.body
    });

    res.status(500).json(formatError(error, 'Erro ao processar lote RPS síncrono'));
  }
}

/**
 * Consultar NFSe por número
 */
async function consultarNfsePorNumero(req, res) {
  try {
    const { numero } = req.params;
    const { cnpjPrestador, inscricaoMunicipal } = req.query;

    if (!cnpjPrestador || !inscricaoMunicipal) {
      return res.status(400).json(formatError(
        new Error('CNPJ do prestador e inscrição municipal são obrigatórios'),
        'Parâmetros obrigatórios'
      ));
    }

    logger.info('Consultando NFSe por número', {
      numero,
      cnpjPrestador,
      inscricaoMunicipal
    });

    const consultaData = {
      NumeroNfse: numero,
      Prestador: {
        Cnpj: cnpjPrestador,
        InscricaoMunicipal: inscricaoMunicipal
      }
    };

    const xmlConsulta = xmlBuilder.buildConsultarNfseXml(consultaData);
    const resultado = await fiorilliClient.consultarNfse(xmlConsulta);

    logger.info('NFSe consultada com sucesso', {
      numero,
      resultado: resultado
    });

    res.json(formatResponse({
      message: 'NFSe consultada com sucesso',
      data: resultado
    }));

  } catch (error) {
    logger.error('Erro ao consultar NFSe', {
      error: error.message,
      numero: req.params.numero
    });

    res.status(500).json(formatError(error, 'Erro ao consultar NFSe'));
  }
}

/**
 * Consultar NFSe com filtros
 */
async function consultarNfse(req, res) {
  try {
    logger.info('Consultando NFSe com filtros', {
      filtros: req.body
    });

    const xmlConsulta = xmlBuilder.buildConsultarNfseXml(req.body);
    const resultado = await fiorilliClient.consultarNfse(xmlConsulta);

    logger.info('Consulta NFSe realizada com sucesso', {
      resultado: resultado
    });

    res.json(formatResponse({
      message: 'Consulta realizada com sucesso',
      data: resultado,
      filtros: req.body
    }));

  } catch (error) {
    logger.error('Erro ao consultar NFSe com filtros', {
      error: error.message,
      filtros: req.body
    });

    res.status(500).json(formatError(error, 'Erro ao consultar NFSe'));
  }
}

/**
 * Cancelar NFSe
 */
async function cancelarNfse(req, res) {
  try {
    logger.info('Iniciando cancelamento de NFSe', {
      numeroNfse: req.body.NumeroNfse,
      codigoCancelamento: req.body.CodigoCancelamento
    });

    const xmlCancelamento = xmlBuilder.buildCancelarNfseXml(req.body);
    const resultado = await fiorilliClient.cancelarNfse(xmlCancelamento);

    logger.info('NFSe cancelada com sucesso', {
      numeroNfse: req.body.NumeroNfse,
      resultado: resultado
    });

    res.json(formatResponse({
      message: 'NFSe cancelada com sucesso',
      data: resultado,
      cancelamentoOriginal: req.body
    }));

  } catch (error) {
    logger.error('Erro ao cancelar NFSe', {
      error: error.message,
      cancelamentoData: req.body
    });

    res.status(500).json(formatError(error, 'Erro ao cancelar NFSe'));
  }
}

/**
 * Consultar status do lote
 */
async function consultarStatusLote(req, res) {
  try {
    const { protocolo } = req.params;
    const { cnpjPrestador, inscricaoMunicipal } = req.query;

    if (!cnpjPrestador || !inscricaoMunicipal) {
      return res.status(400).json(formatError(
        new Error('CNPJ do prestador e inscrição municipal são obrigatórios'),
        'Parâmetros obrigatórios'
      ));
    }

    logger.info('Consultando status do lote', {
      protocolo,
      cnpjPrestador,
      inscricaoMunicipal
    });

    const consultaData = {
      Protocolo: protocolo,
      Prestador: {
        Cnpj: cnpjPrestador,
        InscricaoMunicipal: inscricaoMunicipal
      }
    };

    const xmlConsulta = xmlBuilder.buildConsultarSituacaoLoteXml(consultaData);
    const resultado = await fiorilliClient.consultarSituacaoLoteRps(xmlConsulta);

    logger.info('Status do lote consultado com sucesso', {
      protocolo,
      resultado: resultado
    });

    res.json(formatResponse({
      message: 'Status do lote consultado com sucesso',
      data: resultado
    }));

  } catch (error) {
    logger.error('Erro ao consultar status do lote', {
      error: error.message,
      protocolo: req.params.protocolo
    });

    res.status(500).json(formatError(error, 'Erro ao consultar status do lote'));
  }
}

/**
 * Obter exemplo de RPS
 */
async function obterExemplo(req, res) {
  try {
    const exemplo = {
      Id: 'RPS001_2025',
      Numero: 1,
      Serie: '001',
      Tipo: 1,
      DataEmissao: new Date().toISOString(),
      NaturezaOperacao: 1,
      OptanteSimplesNacional: 1,
      IncentivadorCultural: 2,
      Status: 1,
      Servico: {
        ValorServicos: 1000.00,
        ValorDeducoes: 0.00,
        ValorPis: 0.00,
        ValorCofins: 0.00,
        ValorInss: 0.00,
        ValorIr: 0.00,
        ValorCsll: 0.00,
        IssRetido: 2,
        ValorIss: 50.00,
        ValorIssRetido: 0.00,
        OutrasRetencoes: 0.00,
        BaseCalculo: 1000.00,
        Aliquota: 5.00,
        ValorLiquidoNfse: 950.00,
        DescontoIncondicionado: 0.00,
        DescontoCondicionado: 0.00,
        ItemListaServico: '01.01',
        CodigoCnae: '6201500',
        CodigoTributacaoMunicipio: '010101',
        Discriminacao: 'Desenvolvimento de software sob medida',
        CodigoMunicipio: '3550308'
      },
      Prestador: {
        Cnpj: '12345678000195',
        InscricaoMunicipal: '123456'
      },
      Tomador: {
        Cnpj: '98765432000198',
        InscricaoMunicipal: '654321',
        RazaoSocial: 'Empresa Tomadora LTDA',
        Endereco: {
          Endereco: 'Rua das Flores',
          Numero: '123',
          Complemento: 'Sala 1',
          Bairro: 'Centro',
          CodigoMunicipio: '3550308',
          Uf: 'SP',
          Cep: '01234567'
        },
        Contato: {
          Telefone: '11999999999',
          Email: '<EMAIL>'
        }
      }
    };

    res.json(formatResponse({
      message: 'Exemplo de estrutura RPS',
      data: exemplo,
      observacoes: [
        'Todos os valores monetários devem estar no formato 0.00',
        'Alíquotas devem ser informadas em números inteiros (ex: 5.00, não 0.05)',
        'CNPJ deve conter apenas números',
        'Data de emissão deve estar no formato ISO 8601',
        'Código do município deve seguir tabela IBGE'
      ]
    }));

  } catch (error) {
    logger.error('Erro ao obter exemplo', { error: error.message });
    res.status(500).json(formatError(error, 'Erro ao obter exemplo'));
  }
}

/**
 * Validar dados do RPS
 */
async function validarRps(req, res) {
  try {
    const { validateRpsData } = require('../utils/validators');

    logger.info('Validando dados do RPS', {
      rpsNumero: req.body.Numero
    });

    const validation = validateRpsData(req.body);

    if (validation.isValid) {
      res.json(formatResponse({
        message: 'Dados do RPS são válidos',
        data: {
          valid: true,
          rpsData: req.body
        }
      }));
    } else {
      res.status(400).json(formatResponse({
        message: 'Dados do RPS são inválidos',
        data: {
          valid: false,
          errors: validation.errors,
          rpsData: req.body
        }
      }));
    }

  } catch (error) {
    logger.error('Erro ao validar RPS', {
      error: error.message,
      rpsData: req.body
    });

    res.status(500).json(formatError(error, 'Erro ao validar RPS'));
  }
}

module.exports = {
  gerarNfse,
  enviarLoteRps,
  enviarLoteRpsSincrono,
  consultarNfsePorNumero,
  consultarNfse,
  cancelarNfse,
  consultarStatusLote,
  obterExemplo,
  validarRps
};