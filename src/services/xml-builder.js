/**
 * Construtor de XML para NFSe Fiorilli
 */

const xml2js = require('xml2js');
const logger = require('../utils/logger');

class XmlBuilder {
  constructor() {
    this.builder = new xml2js.Builder({
      headless: false,
      renderOpts: {
        pretty: false,
        indent: '',
        newline: ''
      },
      xmldec: {
        version: '1.0',
        encoding: 'UTF-8'
      }
    });
  }

  /**
   * Construir XML para GerarNfse
   */
  buildGerarNfseXml(rpsData) {
    try {
      logger.debug('Iniciando construção XML GerarNfse', { rpsData });

      // Construir seções separadamente para debug
      const servico = this.buildServico(rpsData.Servico);
      logger.debug('Seção Servico construída', { servico });

      const prestador = this.buildPrestador(rpsData.Prestador);
      logger.debug('Seção Prestador construída', { prestador });

      const tomador = this.buildTomador(rpsData.Tomador);
      logger.debug('Seção Tomador construída', { tomador });

      const xml = {
        GerarNfseEnvio: {
          $: { xmlns: 'http://www.abrasf.org.br/nfse.xsd' },
          Rps: {
            InfRps: {
              $: { Id: rpsData.Id },
              IdentificacaoRps: {
                Numero: rpsData.Numero,
                Serie: rpsData.Serie,
                Tipo: rpsData.Tipo
              },
              DataEmissao: this.formatDateTime(rpsData.DataEmissao),
              NaturezaOperacao: rpsData.NaturezaOperacao,
              OptanteSimplesNacional: rpsData.OptanteSimplesNacional,
              IncentivadorCultural: rpsData.IncentivadorCultural,
              Status: rpsData.Status,
              Servico: servico,
              Prestador: prestador,
              Tomador: tomador
            }
          }
        }
      };

      logger.debug('Objeto XML construído', { xml: JSON.stringify(xml, null, 2) });

      const xmlString = this.builder.buildObject(xml);
      logger.debug('XML GerarNfse construído', { xml: xmlString });

      return xmlString;
    } catch (error) {
      logger.error('Erro ao construir XML GerarNfse', {
        error: error.message,
        stack: error.stack,
        rpsData
      });
      throw new Error(`Erro ao construir XML: ${error.message}`);
    }
  }

  /**
   * Construir XML para RecepcionarLoteRps
   */
  buildRecepcionarLoteRpsXml(loteData) {
    try {
      const xml = {
        EnviarLoteRpsEnvio: {
          $: { xmlns: 'http://www.abrasf.org.br/nfse.xsd' },
          LoteRps: {
            $: { Id: loteData.Id },
            NumeroLote: loteData.NumeroLote,
            Cnpj: loteData.Cnpj,
            InscricaoMunicipal: loteData.InscricaoMunicipal,
            QuantidadeRps: loteData.ListaRps.length,
            ListaRps: {
              Rps: loteData.ListaRps.map(rps => ({
                InfRps: {
                  $: { Id: rps.Id },
                  IdentificacaoRps: {
                    Numero: rps.Numero,
                    Serie: rps.Serie,
                    Tipo: rps.Tipo
                  },
                  DataEmissao: this.formatDateTime(rps.DataEmissao),
                  NaturezaOperacao: rps.NaturezaOperacao,
                  OptanteSimplesNacional: rps.OptanteSimplesNacional,
                  IncentivadorCultural: rps.IncentivadorCultural,
                  Status: rps.Status,
                  Servico: this.buildServico(rps.Servico),
                  Prestador: this.buildPrestador(rps.Prestador),
                  Tomador: this.buildTomador(rps.Tomador)
                }
              }))
            }
          }
        }
      };

      const xmlString = this.builder.buildObject(xml);
      logger.debug('XML RecepcionarLoteRps construído', {
        numeroLote: loteData.NumeroLote,
        quantidadeRps: loteData.ListaRps.length
      });

      return xmlString;
    } catch (error) {
      logger.error('Erro ao construir XML RecepcionarLoteRps', {
        error: error.message,
        loteData
      });
      throw new Error(`Erro ao construir XML do lote: ${error.message}`);
    }
  }

  /**
   * Construir XML para ConsultarNfse
   */
  buildConsultarNfseXml(consultaData) {
    try {
      const xml = {
        ConsultarNfseEnvio: {
          $: { xmlns: 'http://www.abrasf.org.br/nfse.xsd' },
          Prestador: {
            Cnpj: consultaData.Prestador.Cnpj,
            InscricaoMunicipal: consultaData.Prestador.InscricaoMunicipal
          }
        }
      };

      // Adicionar filtros opcionais
      if (consultaData.NumeroNfse) {
        xml.ConsultarNfseEnvio.NumeroNfse = consultaData.NumeroNfse;
      }

      if (consultaData.PeriodoEmissao) {
        xml.ConsultarNfseEnvio.PeriodoEmissao = {
          DataInicial: this.formatDate(consultaData.PeriodoEmissao.DataInicial),
          DataFinal: this.formatDate(consultaData.PeriodoEmissao.DataFinal)
        };
      }

      if (consultaData.Tomador) {
        xml.ConsultarNfseEnvio.Tomador = this.buildTomador(consultaData.Tomador);
      }

      if (consultaData.IntermediarioServico) {
        xml.ConsultarNfseEnvio.IntermediarioServico = this.buildIntermediario(consultaData.IntermediarioServico);
      }

      const xmlString = this.builder.buildObject(xml);
      logger.debug('XML ConsultarNfse construído', { consultaData });

      return xmlString;
    } catch (error) {
      logger.error('Erro ao construir XML ConsultarNfse', {
        error: error.message,
        consultaData
      });
      throw new Error(`Erro ao construir XML de consulta: ${error.message}`);
    }
  }

  /**
   * Construir XML para CancelarNfse
   */
  buildCancelarNfseXml(cancelamentoData) {
    try {
      const xml = {
        CancelarNfseEnvio: {
          $: { xmlns: 'http://www.abrasf.org.br/nfse.xsd' },
          Pedido: {
            InfPedidoCancelamento: {
              $: { Id: cancelamentoData.Id },
              IdentificacaoNfse: {
                Numero: cancelamentoData.NumeroNfse,
                Cnpj: cancelamentoData.Prestador.Cnpj,
                InscricaoMunicipal: cancelamentoData.Prestador.InscricaoMunicipal,
                CodigoMunicipio: cancelamentoData.CodigoMunicipio
              },
              CodigoCancelamento: cancelamentoData.CodigoCancelamento
            }
          }
        }
      };

      const xmlString = this.builder.buildObject(xml);
      logger.debug('XML CancelarNfse construído', {
        numeroNfse: cancelamentoData.NumeroNfse
      });

      return xmlString;
    } catch (error) {
      logger.error('Erro ao construir XML CancelarNfse', {
        error: error.message,
        cancelamentoData
      });
      throw new Error(`Erro ao construir XML de cancelamento: ${error.message}`);
    }
  }

  /**
   * Construir XML para ConsultarSituacaoLoteRps
   */
  buildConsultarSituacaoLoteXml(consultaData) {
    try {
      const xml = {
        ConsultarSituacaoLoteRpsEnvio: {
          $: { xmlns: 'http://www.abrasf.org.br/nfse.xsd' },
          Prestador: {
            Cnpj: consultaData.Prestador.Cnpj,
            InscricaoMunicipal: consultaData.Prestador.InscricaoMunicipal
          },
          Protocolo: consultaData.Protocolo
        }
      };

      const xmlString = this.builder.buildObject(xml);
      logger.debug('XML ConsultarSituacaoLoteRps construído', {
        protocolo: consultaData.Protocolo
      });

      return xmlString;
    } catch (error) {
      logger.error('Erro ao construir XML ConsultarSituacaoLoteRps', {
        error: error.message,
        consultaData
      });
      throw new Error(`Erro ao construir XML de consulta de situação: ${error.message}`);
    }
  }

  /**
   * Construir XML para ConsultarLoteRps
   */
  buildConsultarLoteRpsXml(consultaData) {
    try {
      const xml = {
        ConsultarLoteRpsEnvio: {
          $: { xmlns: 'http://www.abrasf.org.br/nfse.xsd' },
          Prestador: {
            Cnpj: consultaData.Prestador.Cnpj,
            InscricaoMunicipal: consultaData.Prestador.InscricaoMunicipal
          },
          Protocolo: consultaData.Protocolo
        }
      };

      const xmlString = this.builder.buildObject(xml);
      logger.debug('XML ConsultarLoteRps construído', {
        protocolo: consultaData.Protocolo
      });

      return xmlString;
    } catch (error) {
      logger.error('Erro ao construir XML ConsultarLoteRps', {
        error: error.message,
        consultaData
      });
      throw new Error(`Erro ao construir XML de consulta de lote: ${error.message}`);
    }
  }

  /**
   * Construir seção de Serviço
   */
  buildServico(servicoData) {
    return {
      Valores: {
        ValorServicos: this.formatDecimal(servicoData.ValorServicos),
        ValorDeducoes: this.formatDecimal(servicoData.ValorDeducoes || 0),
        ValorPis: this.formatDecimal(servicoData.ValorPis || 0),
        ValorCofins: this.formatDecimal(servicoData.ValorCofins || 0),
        ValorInss: this.formatDecimal(servicoData.ValorInss || 0),
        ValorIr: this.formatDecimal(servicoData.ValorIr || 0),
        ValorCsll: this.formatDecimal(servicoData.ValorCsll || 0),
        IssRetido: servicoData.IssRetido || 2,
        ValorIss: this.formatDecimal(servicoData.ValorIss || 0),
        ValorIssRetido: this.formatDecimal(servicoData.ValorIssRetido || 0),
        OutrasRetencoes: this.formatDecimal(servicoData.OutrasRetencoes || 0),
        BaseCalculo: this.formatDecimal(servicoData.BaseCalculo),
        Aliquota: this.formatDecimal(servicoData.Aliquota),
        ValorLiquidoNfse: this.formatDecimal(servicoData.ValorLiquidoNfse),
        DescontoIncondicionado: this.formatDecimal(servicoData.DescontoIncondicionado || 0),
        DescontoCondicionado: this.formatDecimal(servicoData.DescontoCondicionado || 0)
      },
      ItemListaServico: servicoData.ItemListaServico,
      CodigoCnae: servicoData.CodigoCnae,
      CodigoTributacaoMunicipio: servicoData.CodigoTributacaoMunicipio,
      Discriminacao: this.sanitizeXmlString(servicoData.Discriminacao),
      CodigoMunicipio: servicoData.CodigoMunicipio
    };
  }

  /**
   * Construir seção de Prestador
   */
  buildPrestador(prestadorData) {
    return {
      Cnpj: this.formatCnpj(prestadorData.Cnpj),
      InscricaoMunicipal: prestadorData.InscricaoMunicipal
    };
  }

  /**
   * Construir seção de Tomador
   */
  buildTomador(tomadorData) {
    const tomador = {
      IdentificacaoTomador: {
        CpfCnpj: {}
      }
    };

    // CPF ou CNPJ
    if (tomadorData.Cpf) {
      tomador.IdentificacaoTomador.CpfCnpj.Cpf = this.formatCpf(tomadorData.Cpf);
    } else if (tomadorData.Cnpj) {
      tomador.IdentificacaoTomador.CpfCnpj.Cnpj = this.formatCnpj(tomadorData.Cnpj);
    }

    // Inscrição Municipal (opcional)
    if (tomadorData.InscricaoMunicipal) {
      tomador.IdentificacaoTomador.InscricaoMunicipal = tomadorData.InscricaoMunicipal;
    }

    // Razão Social
    if (tomadorData.RazaoSocial) {
      tomador.RazaoSocial = this.sanitizeXmlString(tomadorData.RazaoSocial);
    }

    // Endereço (opcional)
    if (tomadorData.Endereco) {
      tomador.Endereco = {
        Endereco: this.sanitizeXmlString(tomadorData.Endereco.Endereco),
        Numero: tomadorData.Endereco.Numero,
        Bairro: this.sanitizeXmlString(tomadorData.Endereco.Bairro),
        CodigoMunicipio: tomadorData.Endereco.CodigoMunicipio,
        Uf: tomadorData.Endereco.Uf,
        Cep: this.formatCep(tomadorData.Endereco.Cep)
      };

      // Complemento (opcional)
      if (tomadorData.Endereco.Complemento) {
        tomador.Endereco.Complemento = this.sanitizeXmlString(tomadorData.Endereco.Complemento);
      }
    }

    // Contato (opcional)
    if (tomadorData.Contato) {
      tomador.Contato = {};

      if (tomadorData.Contato.Telefone) {
        tomador.Contato.Telefone = tomadorData.Contato.Telefone;
      }

      if (tomadorData.Contato.Email) {
        tomador.Contato.Email = tomadorData.Contato.Email;
      }
    }

    return tomador;
  }

  /**
   * Construir seção de Intermediário (opcional)
   */
  buildIntermediario(intermediarioData) {
    const intermediario = {
      IdentificacaoIntermediario: {
        CpfCnpj: {}
      }
    };

    // CPF ou CNPJ
    if (intermediarioData.Cpf) {
      intermediario.IdentificacaoIntermediario.CpfCnpj.Cpf = this.formatCpf(intermediarioData.Cpf);
    } else if (intermediarioData.Cnpj) {
      intermediario.IdentificacaoIntermediario.CpfCnpj.Cnpj = this.formatCnpj(intermediarioData.Cnpj);
    }

    // Inscrição Municipal (opcional)
    if (intermediarioData.InscricaoMunicipal) {
      intermediario.IdentificacaoIntermediario.InscricaoMunicipal = intermediarioData.InscricaoMunicipal;
    }

    // Razão Social
    if (intermediarioData.RazaoSocial) {
      intermediario.RazaoSocial = this.sanitizeXmlString(intermediarioData.RazaoSocial);
    }

    return intermediario;
  }

  /**
   * Formatar valor decimal
   */
  formatDecimal(value, decimals = 2) {
    if (value === null || value === undefined) {
      return '0.00';
    }
    return parseFloat(value).toFixed(decimals);
  }

  /**
   * Formatar data e hora
   */
  formatDateTime(dateTime) {
    if (!dateTime) {
      return new Date().toISOString().slice(0, 19);
    }

    if (typeof dateTime === 'string') {
      return new Date(dateTime).toISOString().slice(0, 19);
    }

    return dateTime.toISOString().slice(0, 19);
  }

  /**
   * Formatar data
   */
  formatDate(date) {
    if (!date) {
      return new Date().toISOString().slice(0, 10);
    }

    if (typeof date === 'string') {
      return new Date(date).toISOString().slice(0, 10);
    }

    return date.toISOString().slice(0, 10);
  }

  /**
   * Formatar CNPJ
   */
  formatCnpj(cnpj) {
    if (!cnpj) return '';
    return String(cnpj).replace(/\D/g, '');
  }

  /**
   * Formatar CPF
   */
  formatCpf(cpf) {
    if (!cpf) return '';
    return String(cpf).replace(/\D/g, '');
  }

  /**
   * Formatar CEP
   */
  formatCep(cep) {
    if (!cep) return '';
    return cep.replace(/\D/g, '');
  }

  /**
   * Sanitizar string para XML
   */
  sanitizeXmlString(str) {
    if (!str) return '';

    return str
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;')
      .trim();
  }

  /**
   * Validar XML gerado
   */
  validateXml(xmlString) {
    try {
      // Verificar se é um XML válido
      const parser = new xml2js.Parser();
      parser.parseString(xmlString, (err, result) => {
        if (err) {
          throw new Error(`XML inválido: ${err.message}`);
        }
      });

      // Verificar se não está vazio
      if (!xmlString || xmlString.trim().length === 0) {
        throw new Error('XML vazio');
      }

      // Verificar se contém namespace ABRASF
      if (!xmlString.includes('http://www.abrasf.org.br/nfse.xsd')) {
        throw new Error('XML não contém namespace ABRASF obrigatório');
      }

      return true;
    } catch (error) {
      logger.error('Erro na validação do XML', {
        error: error.message,
        xml: xmlString
      });
      throw error;
    }
  }
}

module.exports = XmlBuilder;