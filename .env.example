# Configurações do Servidor
NODE_ENV=development
PORT=3000
LOG_LEVEL=info

# Ambiente (homologacao ou producao)
FIORILLI_AMBIENTE=homologacao

# Configurações Fiorilli - Produção
FIORILLI_WSDL_URL=https://sua-prefeitura.com/IssWeb-ejb/IssWebWS/IssWebWS?wsdl
FIORILLI_USUARIO=seu_usuario
FIORILLI_SENHA=sua_senha
FIORILLI_CNPJ=seu_cnpj_sem_formatacao
FIORILLI_INSCRICAO=sua_inscricao_municipal

# Configurações de Segurança
API_KEY=sua_chave_api_opcional
JWT_SECRET=seu_jwt_secret_opcional

# Configurações de Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Configurações de Timeout
REQUEST_TIMEOUT=30000
SOAP_TIMEOUT=30000

# Configurações de Retry
MAX_RETRIES=3
RETRY_DELAY=1000

# Configurações de Cache (opcional)
REDIS_URL=redis://localhost:6379
CACHE_TTL=300

# Configurações de Monitoramento (opcional)
ENABLE_METRICS=true
METRICS_PORT=9090

# Configurações de Certificado Digital (se necessário)
CERT_PATH=./certificates/certificado.p12
CERT_PASSWORD=senha_do_certificado
