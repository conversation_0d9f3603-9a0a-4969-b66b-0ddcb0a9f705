{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  level: 'warn',
  message: 'Swagger documentation not available:',
  timestamp: '2025-07-10 22:58:13'
}
{
  error: TypeError: app.use() requires a middleware function
      at Function.use (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/application.js:217:11)
      at Object.<anonymous> (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/server.js:83:5)
      at Module._compile (node:internal/modules/cjs/loader:1554:14)
      at Object..js (node:internal/modules/cjs/loader:1706:10)
      at Module.load (node:internal/modules/cjs/loader:1289:32)
      at Function._load (node:internal/modules/cjs/loader:1108:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
      at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
      at node:internal/main/run_main_module:36:49,
  level: 'error',
  message: 'uncaughtException: app.use() requires a middleware function\n' +
    'TypeError: app.use() requires a middleware function\n' +
    '    at Function.use (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/application.js:217:11)\n' +
    '    at Object.<anonymous> (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/server.js:83:5)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1706:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1289:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1108:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n' +
    '    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)\n' +
    '    at node:internal/main/run_main_module:36:49',
  stack: 'TypeError: app.use() requires a middleware function\n' +
    '    at Function.use (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/application.js:217:11)\n' +
    '    at Object.<anonymous> (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/server.js:83:5)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1706:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1289:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1108:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n' +
    '    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)\n' +
    '    at node:internal/main/run_main_module:36:49',
  exception: true,
  date: 'Thu Jul 10 2025 22:58:13 GMT-0400 (Horário Padrão do Amazonas)',
  process: {
    pid: 3660357,
    uid: 1000,
    gid: 1000,
    cwd: '/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10',
    execPath: '/home/<USER>/.nvm/versions/node/v22.14.0/bin/node',
    version: 'v22.14.0',
    argv: [
      '/home/<USER>/.nvm/versions/node/v22.14.0/bin/node',
      '/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/server.js'
    ],
    memoryUsage: {
      rss: 88920064,
      heapTotal: 34836480,
      heapUsed: 23551152,
      external: 3281500,
      arrayBuffers: 65920
    }
  },
  os: { loadavg: [ 1.62, 1.43, 1.34 ], uptime: 225757.87 },
  trace: [
    {
      column: 11,
      file: '/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/application.js',
      function: 'Function.use',
      line: 217,
      method: 'use',
      native: false
    },
    {
      column: 5,
      file: '/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/server.js',
      function: null,
      line: 83,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1554,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1706,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1289,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1108,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 220,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/modules/run_main',
      function: 'Function.executeUserEntryPoint [as runMain]',
      line: 170,
      method: 'executeUserEntryPoint [as runMain]',
      native: false
    },
    {
      column: 49,
      file: 'node:internal/main/run_main_module',
      function: null,
      line: 36,
      method: null,
      native: false
    }
  ],
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-10 22:58:13'
}
{
  message: '🚀 Servidor NFSe Fiorilli iniciado na porta 3000',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '📚 Documentação disponível em: http://localhost:3000/api-docs',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '🏥 Health check disponível em: http://localhost:3000/health',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '🌍 Ambiente: test',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "GET /health HTTP/1.1" 200 114 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "GET / HTTP/1.1" 200 433 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  errors: [
    {
      field: 'Servico.ValorLiquidoNfse',
      message: 'Valor líquido calculado (1000.00) não confere com o informado (950)',
      expected: '1000.00',
      received: 950
    }
  ],
  body: {
    Id: 'RPS_TEST_001',
    Numero: 1,
    Serie: '001',
    Tipo: 1,
    DataEmissao: '2025-06-01T14:00:00.000Z',
    NaturezaOperacao: 1,
    OptanteSimplesNacional: 1,
    IncentivadorCultural: 2,
    Status: 1,
    Servico: {
      ValorServicos: 1000,
      ValorDeducoes: 0,
      ValorPis: 0,
      ValorCofins: 0,
      ValorInss: 0,
      ValorIr: 0,
      ValorCsll: 0,
      IssRetido: 2,
      ValorIss: 50,
      ValorIssRetido: 0,
      OutrasRetencoes: 0,
      BaseCalculo: 1000,
      Aliquota: 5,
      ValorLiquidoNfse: 950,
      DescontoIncondicionado: 0,
      DescontoCondicionado: 0,
      ItemListaServico: '01.01',
      CodigoCnae: '6201500',
      CodigoTributacaoMunicipio: '010101',
      Discriminacao: 'Desenvolvimento de software sob medida para teste',
      CodigoMunicipio: '3550308'
    },
    Prestador: { Cnpj: '01001001000113', InscricaoMunicipal: '15000' },
    Tomador: {
      Cnpj: '12345678000195',
      InscricaoMunicipal: '123456',
      RazaoSocial: 'EMPRESA TESTE LTDA',
      Endereco: {
        Endereco: 'RUA DE TESTE',
        Numero: '123',
        Complemento: 'SALA 1',
        Bairro: 'CENTRO',
        CodigoMunicipio: '3550308',
        Uf: 'SP',
        Cep: '01234567'
      },
      Contato: { Telefone: '11999999999', Email: '<EMAIL>' }
    }
  },
  level: 'warn',
  message: 'Validação customizada falhou',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "POST /api/nfse/validar HTTP/1.1" 400 238 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  errors: [
    {
      field: 'Numero',
      message: 'Número do RPS é obrigatório',
      value: undefined
    }
  ],
  body: {
    Id: 'RPS_TEST_001',
    Serie: '001',
    Tipo: 1,
    DataEmissao: '2025-06-01T10:00:00',
    NaturezaOperacao: 1,
    OptanteSimplesNacional: 1,
    IncentivadorCultural: 2,
    Status: 1,
    Servico: {
      ValorServicos: 1000,
      ValorDeducoes: 0,
      ValorPis: 0,
      ValorCofins: 0,
      ValorInss: 0,
      ValorIr: 0,
      ValorCsll: 0,
      IssRetido: 2,
      ValorIss: 50,
      ValorIssRetido: 0,
      OutrasRetencoes: 0,
      BaseCalculo: 1000,
      Aliquota: 5,
      ValorLiquidoNfse: 950,
      DescontoIncondicionado: 0,
      DescontoCondicionado: 0,
      ItemListaServico: '01.01',
      CodigoCnae: '6201500',
      CodigoTributacaoMunicipio: '010101',
      Discriminacao: 'Desenvolvimento de software sob medida para teste',
      CodigoMunicipio: '3550308'
    },
    Prestador: { Cnpj: '01001001000113', InscricaoMunicipal: '15000' },
    Tomador: {
      Cnpj: '12345678000195',
      InscricaoMunicipal: '123456',
      RazaoSocial: 'EMPRESA TESTE LTDA',
      Endereco: {
        Endereco: 'RUA DE TESTE',
        Numero: '123',
        Complemento: 'SALA 1',
        Bairro: 'CENTRO',
        CodigoMunicipio: '3550308',
        Uf: 'SP',
        Cep: '01234567'
      },
      Contato: { Telefone: '11999999999', Email: '<EMAIL>' }
    }
  },
  level: 'warn',
  message: 'Validação falhou para GerarNfse',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "POST /api/nfse/validar HTTP/1.1" 400 142 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  errors: [
    {
      field: 'Servico.ValorIss',
      message: 'ISS calculado (50.00) não confere com o informado (100)',
      expected: '50.00',
      received: 100
    },
    {
      field: 'Servico.ValorLiquidoNfse',
      message: 'Valor líquido calculado (1000.00) não confere com o informado (950)',
      expected: '1000.00',
      received: 950
    }
  ],
  body: {
    Id: 'RPS_TEST_001',
    Numero: 1,
    Serie: '001',
    Tipo: 1,
    DataEmissao: '2025-06-01T14:00:00.000Z',
    NaturezaOperacao: 1,
    OptanteSimplesNacional: 1,
    IncentivadorCultural: 2,
    Status: 1,
    Servico: {
      ValorServicos: 1000,
      ValorDeducoes: 0,
      ValorPis: 0,
      ValorCofins: 0,
      ValorInss: 0,
      ValorIr: 0,
      ValorCsll: 0,
      IssRetido: 2,
      ValorIss: 100,
      ValorIssRetido: 0,
      OutrasRetencoes: 0,
      BaseCalculo: 1000,
      Aliquota: 5,
      ValorLiquidoNfse: 950,
      DescontoIncondicionado: 0,
      DescontoCondicionado: 0,
      ItemListaServico: '01.01',
      CodigoCnae: '6201500',
      CodigoTributacaoMunicipio: '010101',
      Discriminacao: 'Desenvolvimento de software sob medida para teste',
      CodigoMunicipio: '3550308'
    },
    Prestador: { Cnpj: '01001001000113', InscricaoMunicipal: '15000' },
    Tomador: {
      Cnpj: '12345678000195',
      InscricaoMunicipal: '123456',
      RazaoSocial: 'EMPRESA TESTE LTDA',
      Endereco: {
        Endereco: 'RUA DE TESTE',
        Numero: '123',
        Complemento: 'SALA 1',
        Bairro: 'CENTRO',
        CodigoMunicipio: '3550308',
        Uf: 'SP',
        Cep: '01234567'
      },
      Contato: { Telefone: '11999999999', Email: '<EMAIL>' }
    }
  },
  level: 'warn',
  message: 'Validação customizada falhou',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "POST /api/nfse/validar HTTP/1.1" 400 370 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "GET /api/nfse/exemplo HTTP/1.1" 200 - "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  errors: [
    {
      field: 'Servico.ValorIss',
      message: 'ISS calculado (50.00) não confere com o informado (100)',
      expected: '50.00',
      received: 100
    },
    {
      field: 'Servico.ValorLiquidoNfse',
      message: 'Valor líquido calculado (1000.00) não confere com o informado (950)',
      expected: '1000.00',
      received: 950
    }
  ],
  body: {
    Id: 'RPS_TEST_001',
    Numero: 1,
    Serie: '001',
    Tipo: 1,
    DataEmissao: '2025-06-01T14:00:00.000Z',
    NaturezaOperacao: 1,
    OptanteSimplesNacional: 1,
    IncentivadorCultural: 2,
    Status: 1,
    Servico: {
      ValorServicos: 1000,
      ValorDeducoes: 0,
      ValorPis: 0,
      ValorCofins: 0,
      ValorInss: 0,
      ValorIr: 0,
      ValorCsll: 0,
      IssRetido: 2,
      ValorIss: 100,
      ValorIssRetido: 0,
      OutrasRetencoes: 0,
      BaseCalculo: 1000,
      Aliquota: 5,
      ValorLiquidoNfse: 950,
      DescontoIncondicionado: 0,
      DescontoCondicionado: 0,
      ItemListaServico: '01.01',
      CodigoCnae: '6201500',
      CodigoTributacaoMunicipio: '010101',
      Discriminacao: 'Desenvolvimento de software sob medida para teste',
      CodigoMunicipio: '3550308'
    },
    Prestador: { Cnpj: '01001001000113', InscricaoMunicipal: '15000' },
    Tomador: {
      Cnpj: '12345678000195',
      InscricaoMunicipal: '123456',
      RazaoSocial: 'EMPRESA TESTE LTDA',
      Endereco: {
        Endereco: 'RUA DE TESTE',
        Numero: '123',
        Complemento: 'SALA 1',
        Bairro: 'CENTRO',
        CodigoMunicipio: '3550308',
        Uf: 'SP',
        Cep: '01234567'
      },
      Contato: { Telefone: '11999999999', Email: '<EMAIL>' }
    }
  },
  level: 'warn',
  message: 'Validação customizada falhou',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "POST /api/nfse/gerar HTTP/1.1" 400 370 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  errors: [
    {
      field: 'Numero',
      message: 'Número do RPS é obrigatório',
      value: undefined
    },
    {
      field: 'Serie',
      message: 'Série do RPS é obrigatória',
      value: undefined
    },
    {
      field: 'Tipo',
      message: 'Tipo do RPS é obrigatório',
      value: undefined
    },
    {
      field: 'DataEmissao',
      message: 'Data de emissão é obrigatória',
      value: undefined
    },
    {
      field: 'NaturezaOperacao',
      message: 'Natureza da operação é obrigatória',
      value: undefined
    },
    {
      field: 'OptanteSimplesNacional',
      message: 'Optante Simples Nacional é obrigatório',
      value: undefined
    },
    {
      field: 'IncentivadorCultural',
      message: 'Incentivador Cultural é obrigatório',
      value: undefined
    },
    {
      field: 'Status',
      message: 'Status é obrigatório',
      value: undefined
    },
    {
      field: 'Servico',
      message: '"Servico" is required',
      value: undefined
    },
    {
      field: 'Prestador',
      message: '"Prestador" is required',
      value: undefined
    },
    {
      field: 'Tomador',
      message: '"Tomador" is required',
      value: undefined
    }
  ],
  body: { Id: 'TESTE' },
  level: 'warn',
  message: 'Validação falhou para GerarNfse',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "POST /api/nfse/gerar HTTP/1.1" 400 805 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "GET /api/nfse/consultar/123 HTTP/1.1" 400 200 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  numero: '123',
  cnpjPrestador: '01001001000113',
  inscricaoMunicipal: '15000',
  level: 'info',
  message: 'Consultando NFSe por número',
  timestamp: '2025-07-10 23:09:59'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  error: "Cannot read properties of undefined (reading 'xmlBuilder')",
  numero: '123',
  level: 'error',
  message: 'Erro ao consultar NFSe',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "GET /api/nfse/consultar/123?cnpjPrestador=01001001000113&inscricaoMunicipal=15000 HTTP/1.1" 500 195 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  error: "Cannot read properties of undefined (reading 'NumeroLote')",
  stack: "TypeError: Cannot read properties of undefined (reading 'NumeroLote')\n" +
    '    at NumeroLote (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/src/controllers/nfse-controller.js:62:26)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at next (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:149:13)\n' +
    '    at Route.dispatch (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at /home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:284:15\n' +
    '    at Function.process_params (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:346:12)\n' +
    '    at next (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:280:10)\n' +
    '    at next (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/src/routes/nfse-routes.js:13:3)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at trim_prefix (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:328:13)\n' +
    '    at /home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:286:9\n' +
    '    at Function.process_params (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:346:12)\n' +
    '    at next (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:280:10)\n' +
    '    at Function.handle (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:175:3)\n' +
    '    at router (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:47:12)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at trim_prefix (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:328:13)\n' +
    '    at /home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:286:9\n' +
    '    at Function.process_params (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:346:12)\n' +
    '    at next (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:280:10)\n' +
    '    at urlencodedParser (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/body-parser/lib/types/urlencoded.js:85:7)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at trim_prefix (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:328:13)\n' +
    '    at /home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:286:9\n' +
    '    at Function.process_params (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:346:12)\n' +
    '    at next (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:280:10)\n' +
    '    at /home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/body-parser/lib/read.js:137:5\n' +
    '    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n' +
    '    at invokeCallback (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/raw-body/index.js:238:16)\n' +
    '    at done (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/raw-body/index.js:227:7)\n' +
    '    at IncomingMessage.onEnd (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/raw-body/index.js:287:7)\n' +
    '    at IncomingMessage.emit (node:events:518:28)\n' +
    '    at endReadableNT (node:internal/streams/readable:1698:12)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:90:21)',
  loteData: { NumeroLote: 1 },
  level: 'error',
  message: 'Erro ao enviar lote RPS',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "POST /api/nfse/lote HTTP/1.1" 500 196 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  numeroLote: 1,
  quantidadeRps: 0,
  level: 'info',
  message: 'Iniciando envio de lote RPS',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "POST /api/nfse/lote HTTP/1.1" 400 164 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  numeroLote: 1,
  quantidadeRps: 51,
  level: 'info',
  message: 'Iniciando envio de lote RPS',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "POST /api/nfse/lote HTTP/1.1" 400 167 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  errors: [
    { field: 'Id', message: '"Id" is required', value: undefined },
    {
      field: 'CodigoMunicipio',
      message: '"CodigoMunicipio" is required',
      value: undefined
    },
    {
      field: 'CodigoCancelamento',
      message: '"CodigoCancelamento" is required',
      value: undefined
    },
    {
      field: 'Prestador',
      message: '"Prestador" is required',
      value: undefined
    }
  ],
  body: { NumeroNfse: '123' },
  level: 'warn',
  message: 'Validação falhou para CancelarNfse',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "POST /api/nfse/cancelar HTTP/1.1" 400 339 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "GET /api/nfse/status/PROTOCOLO123 HTTP/1.1" 400 200 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  method: 'GET',
  path: '/',
  statusCode: 404,
  duration: '0ms',
  ip: '::ffff:127.0.0.1',
  userAgent: undefined,
  level: 'info',
  message: 'NFSe API Request',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "GET /api/nfse/inexistente HTTP/1.1" 404 121 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "GET /health HTTP/1.1" 200 114 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "GET /health HTTP/1.1" 200 114 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "GET /health HTTP/1.1" 200 114 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "GET /health HTTP/1.1" 200 113 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "GET /health HTTP/1.1" 200 114 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "GET /health HTTP/1.1" 200 114 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "GET /health HTTP/1.1" 200 114 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "GET /health HTTP/1.1" 200 114 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "GET /health HTTP/1.1" 200 114 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '::ffff:127.0.0.1 - - [11/Jul/2025:03:09:59 +0000] "GET /health HTTP/1.1" 200 114 "-" "-"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'test',
  timestamp: '2025-07-10 23:09:59'
}
{
  message: '🚀 Servidor NFSe Fiorilli iniciado na porta 3000',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-10 23:12:14'
}
{
  message: '📚 Documentação disponível em: http://localhost:3000/api-docs',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-10 23:12:14'
}
{
  message: '🏥 Health check disponível em: http://localhost:3000/health',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-10 23:12:14'
}
{
  message: '🌍 Ambiente: development',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-10 23:12:14'
}
{
  message: '🚀 Servidor NFSe Fiorilli iniciado na porta 3000',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:15:10'
}
{
  message: '📚 Documentação disponível em: http://localhost:3000/api-docs',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:15:10'
}
{
  message: '🏥 Health check disponível em: http://localhost:3000/health',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:15:10'
}
{
  message: '🌍 Ambiente: development',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:15:10'
}
{
  message: '::1 - - [11/Jul/2025:14:15:16 +0000] "GET /api-docs HTTP/1.1" 301 158 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:15:16'
}
{
  message: '::1 - - [11/Jul/2025:14:15:16 +0000] "GET /api-docs/ HTTP/1.1" 200 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:15:16'
}
{
  message: '::1 - - [11/Jul/2025:14:15:16 +0000] "GET /api-docs/swagger-ui-init.js HTTP/1.1" 200 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:15:16'
}
{
  message: '::1 - - [11/Jul/2025:14:15:16 +0000] "GET /api-docs/swagger-ui.css HTTP/1.1" 200 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:15:16'
}
{
  message: '::1 - - [11/Jul/2025:14:15:16 +0000] "GET /api-docs/swagger-ui-standalone-preset.js HTTP/1.1" 200 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:15:16'
}
{
  message: '::1 - - [11/Jul/2025:14:15:16 +0000] "GET /api-docs/swagger-ui-bundle.js HTTP/1.1" 200 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:15:16'
}
{
  message: '::1 - - [11/Jul/2025:14:15:16 +0000] "GET /api-docs/favicon-32x32.png HTTP/1.1" 200 628 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:15:16'
}
{
  message: '::1 - - [11/Jul/2025:14:15:18 +0000] "GET /firebase-messaging-sw.js HTTP/1.1" 404 125 "http://localhost:3000/firebase-messaging-sw.js" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:15:18'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  rpsNumero: 1,
  prestadorCnpj: '56135432000164',
  level: 'info',
  message: 'Iniciando geração de NFSe',
  timestamp: '2025-07-11 10:16:05'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  error: "Cannot read properties of undefined (reading 'xmlBuilder')",
  stack: "TypeError: Cannot read properties of undefined (reading 'xmlBuilder')\n" +
    '    at gerarNfse (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/src/controllers/nfse-controller.js:27:27)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at next (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:149:13)\n' +
    '    at validateGerarNfse (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/src/middleware/validation.js:265:3)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at next (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:149:13)\n' +
    '    at Route.dispatch (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at /home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:284:15\n' +
    '    at Function.process_params (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:346:12)',
  rpsData: {
    Id: 'RPS001_2025',
    Numero: 1,
    Serie: '001',
    Tipo: 1,
    DataEmissao: '2025-06-02T02:46:19.000Z',
    NaturezaOperacao: 1,
    OptanteSimplesNacional: 1,
    IncentivadorCultural: 2,
    Status: 1,
    Servico: {
      ValorServicos: 7000,
      ValorDeducoes: 0,
      ValorPis: 0,
      ValorCofins: 0,
      ValorInss: 0,
      ValorIr: 0,
      ValorCsll: 0,
      IssRetido: 2,
      ValorIss: 140.7,
      ValorIssRetido: 0,
      OutrasRetencoes: 0,
      BaseCalculo: 7000,
      Aliquota: 2.01,
      ValorLiquidoNfse: 7000,
      DescontoIncondicionado: 0,
      DescontoCondicionado: 0,
      ItemListaServico: '01.08',
      CodigoCnae: '6319400',
      CodigoTributacaoMunicipio: '000001.0000008',
      Discriminacao: 'Planejamento, confecção, manutenção e atualização de páginas eletrônicas',
      CodigoMunicipio: '5105622'
    },
    Prestador: { Cnpj: '56135432000164', InscricaoMunicipal: '000041600' },
    Tomador: {
      Cnpj: '33000071000143',
      RazaoSocial: 'QI PLUS SISTEMAS LTDA',
      Endereco: {
        Endereco: 'R PACAEMBU',
        Numero: '30',
        Complemento: 'SALA 08',
        Bairro: 'RESIDENCIAL PACAEMBU',
        CodigoMunicipio: '3523909',
        Uf: 'SP',
        Cep: '13295468'
      }
    }
  },
  level: 'error',
  message: 'Erro ao gerar NFSe',
  timestamp: '2025-07-11 10:16:05'
}
{
  message: '::1 - - [11/Jul/2025:14:16:05 +0000] "POST /api/nfse/gerar HTTP/1.1" 500 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:16:05'
}
{
  message: '🚀 Servidor NFSe Fiorilli iniciado na porta 3000',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:53:46'
}
{
  message: '📚 Documentação disponível em: http://localhost:3000/api-docs',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:53:46'
}
{
  message: '🏥 Health check disponível em: http://localhost:3000/health',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:53:46'
}
{
  message: '🌍 Ambiente: development',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:53:46'
}
{
  message: '::1 - - [12/Jul/2025:03:53:49 +0000] "GET /health HTTP/1.1" 200 121 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:53:49'
}
{
  message: '::1 - - [12/Jul/2025:03:53:49 +0000] "GET /favicon.ico HTTP/1.1" 404 112 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:53:49'
}
{
  message: '::1 - - [12/Jul/2025:03:53:51 +0000] "GET /firebase-messaging-sw.js HTTP/1.1" 404 125 "http://localhost:3000/firebase-messaging-sw.js" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:53:51'
}
{
  message: '::1 - - [12/Jul/2025:03:53:59 +0000] "GET /health HTTP/1.1" 200 122 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:53:59'
}
{
  message: '::1 - - [12/Jul/2025:03:54:01 +0000] "GET /api-docs/ HTTP/1.1" 304 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:54:01'
}
{
  message: '::1 - - [12/Jul/2025:03:54:01 +0000] "GET /api-docs/swagger-ui.css HTTP/1.1" 304 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:54:01'
}
{
  message: '::1 - - [12/Jul/2025:03:54:01 +0000] "GET /api-docs/swagger-ui-init.js HTTP/1.1" 304 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:54:01'
}
{
  message: '::1 - - [12/Jul/2025:03:54:01 +0000] "GET /api-docs/swagger-ui-bundle.js HTTP/1.1" 304 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:54:01'
}
{
  message: '::1 - - [12/Jul/2025:03:54:01 +0000] "GET /api-docs/swagger-ui-standalone-preset.js HTTP/1.1" 304 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:54:01'
}
{
  message: '::1 - - [12/Jul/2025:03:54:01 +0000] "GET /firebase-messaging-sw.js HTTP/1.1" 404 125 "http://localhost:3000/firebase-messaging-sw.js" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:54:01'
}
{
  message: '::1 - - [12/Jul/2025:03:54:03 +0000] "GET /firebase-messaging-sw.js HTTP/1.1" 404 125 "http://localhost:3000/firebase-messaging-sw.js" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:54:03'
}
{
  message: 'Recebido SIGINT, encerrando logger...',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:01:10'
}
{
  message: '🚀 Servidor NFSe Fiorilli iniciado na porta 3000',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:01:12'
}
{
  message: '📚 Documentação disponível em: http://localhost:3000/api-docs',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:01:12'
}
{
  message: '🏥 Health check disponível em: http://localhost:3000/health',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:01:12'
}
{
  message: '🌍 Ambiente: development',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:01:12'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  errors: [
    {
      field: 'Servico.ValorLiquidoNfse',
      message: 'Valor líquido calculado (1000.00) não confere com o informado (950)',
      expected: '1000.00',
      received: 950
    }
  ],
  body: {
    Id: 'RPS001_2025',
    Numero: 1,
    Serie: '001',
    Tipo: 1,
    DataEmissao: '2025-06-02T02:46:19.000Z',
    NaturezaOperacao: 1,
    OptanteSimplesNacional: 1,
    IncentivadorCultural: 2,
    Status: 1,
    Servico: {
      ValorServicos: 1000,
      BaseCalculo: 1000,
      Aliquota: 5,
      ValorIss: 50,
      ValorLiquidoNfse: 950,
      ItemListaServico: '01.01',
      Discriminacao: 'Desenvolvimento de software',
      CodigoMunicipio: '3550308',
      ValorDeducoes: 0,
      ValorPis: 0,
      ValorCofins: 0,
      ValorInss: 0,
      ValorIr: 0,
      ValorCsll: 0,
      IssRetido: 2,
      ValorIssRetido: 0,
      OutrasRetencoes: 0,
      DescontoIncondicionado: 0,
      DescontoCondicionado: 0
    },
    Prestador: { Cnpj: '12345678000195', InscricaoMunicipal: '123456' },
    Tomador: { Cnpj: '98765432000198', RazaoSocial: 'EMPRESA CLIENTE LTDA' }
  },
  level: 'warn',
  message: 'Validação customizada falhou',
  timestamp: '2025-07-12 00:02:54'
}
{
  message: '::1 - - [12/Jul/2025:04:02:54 +0000] "POST /api/nfse/gerar HTTP/1.1" 400 238 "-" "PostmanRuntime/7.43.0"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:02:54'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  rpsNumero: 1,
  prestadorCnpj: '12345678000195',
  level: 'info',
  message: 'Iniciando geração de NFSe',
  timestamp: '2025-07-12 00:03:26'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  error: "Cannot read properties of undefined (reading 'xmlBuilder')",
  stack: "TypeError: Cannot read properties of undefined (reading 'xmlBuilder')\n" +
    '    at gerarNfse (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/src/controllers/nfse-controller.js:27:27)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at next (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:149:13)\n' +
    '    at validateGerarNfse (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/src/middleware/validation.js:265:3)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at next (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:149:13)\n' +
    '    at Route.dispatch (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at /home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:284:15\n' +
    '    at Function.process_params (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:346:12)',
  rpsData: {
    Id: 'RPS001_2025',
    Numero: 1,
    Serie: '001',
    Tipo: 1,
    DataEmissao: '2025-06-02T02:46:19.000Z',
    NaturezaOperacao: 1,
    OptanteSimplesNacional: 1,
    IncentivadorCultural: 2,
    Status: 1,
    Servico: {
      ValorServicos: 1000,
      BaseCalculo: 1000,
      Aliquota: 5,
      ValorIss: 50,
      ValorLiquidoNfse: 1000,
      ItemListaServico: '01.01',
      Discriminacao: 'Desenvolvimento de software',
      CodigoMunicipio: '3550308',
      ValorDeducoes: 0,
      ValorPis: 0,
      ValorCofins: 0,
      ValorInss: 0,
      ValorIr: 0,
      ValorCsll: 0,
      IssRetido: 2,
      ValorIssRetido: 0,
      OutrasRetencoes: 0,
      DescontoIncondicionado: 0,
      DescontoCondicionado: 0
    },
    Prestador: { Cnpj: '12345678000195', InscricaoMunicipal: '123456' },
    Tomador: { Cnpj: '98765432000198', RazaoSocial: 'EMPRESA CLIENTE LTDA' }
  },
  level: 'error',
  message: 'Erro ao gerar NFSe',
  timestamp: '2025-07-12 00:03:26'
}
{
  message: '::1 - - [12/Jul/2025:04:03:26 +0000] "POST /api/nfse/gerar HTTP/1.1" 500 - "-" "PostmanRuntime/7.43.0"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:03:26'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  numero: '19',
  cnpjPrestador: '56135432000164',
  inscricaoMunicipal: '000041600',
  level: 'info',
  message: 'Consultando NFSe por número',
  timestamp: '2025-07-12 00:04:19'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  error: "Cannot read properties of undefined (reading 'xmlBuilder')",
  numero: '19',
  level: 'error',
  message: 'Erro ao consultar NFSe',
  timestamp: '2025-07-12 00:04:19'
}
{
  message: '::1 - - [12/Jul/2025:04:04:19 +0000] "GET /api/nfse/consultar/19?cnpjPrestador=56135432000164&inscricaoMunicipal=000041600 HTTP/1.1" 500 - "-" "PostmanRuntime/7.43.0"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:04:19'
}
