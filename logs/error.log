{
  error: TypeError: app.use() requires a middleware function
      at Function.use (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/application.js:217:11)
      at Object.<anonymous> (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/server.js:83:5)
      at Module._compile (node:internal/modules/cjs/loader:1554:14)
      at Object..js (node:internal/modules/cjs/loader:1706:10)
      at Module.load (node:internal/modules/cjs/loader:1289:32)
      at Function._load (node:internal/modules/cjs/loader:1108:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
      at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
      at node:internal/main/run_main_module:36:49,
  level: 'error',
  message: 'uncaughtException: app.use() requires a middleware function\n' +
    'TypeError: app.use() requires a middleware function\n' +
    '    at Function.use (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/application.js:217:11)\n' +
    '    at Object.<anonymous> (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/server.js:83:5)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1706:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1289:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1108:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n' +
    '    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)\n' +
    '    at node:internal/main/run_main_module:36:49',
  stack: 'TypeError: app.use() requires a middleware function\n' +
    '    at Function.use (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/application.js:217:11)\n' +
    '    at Object.<anonymous> (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/server.js:83:5)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1706:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1289:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1108:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n' +
    '    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)\n' +
    '    at node:internal/main/run_main_module:36:49',
  exception: true,
  date: 'Thu Jul 10 2025 22:58:13 GMT-0400 (Horário Padrão do Amazonas)',
  process: {
    pid: 3660357,
    uid: 1000,
    gid: 1000,
    cwd: '/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10',
    execPath: '/home/<USER>/.nvm/versions/node/v22.14.0/bin/node',
    version: 'v22.14.0',
    argv: [
      '/home/<USER>/.nvm/versions/node/v22.14.0/bin/node',
      '/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/server.js'
    ],
    memoryUsage: {
      rss: 88920064,
      heapTotal: 34836480,
      heapUsed: 23551152,
      external: 3281500,
      arrayBuffers: 65920
    }
  },
  os: { loadavg: [ 1.62, 1.43, 1.34 ], uptime: 225757.87 },
  trace: [
    {
      column: 11,
      file: '/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/application.js',
      function: 'Function.use',
      line: 217,
      method: 'use',
      native: false
    },
    {
      column: 5,
      file: '/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/server.js',
      function: null,
      line: 83,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1554,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1706,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1289,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1108,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 220,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/modules/run_main',
      function: 'Function.executeUserEntryPoint [as runMain]',
      line: 170,
      method: 'executeUserEntryPoint [as runMain]',
      native: false
    },
    {
      column: 49,
      file: 'node:internal/main/run_main_module',
      function: null,
      line: 36,
      method: null,
      native: false
    }
  ],
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-10 22:58:13'
}
