{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  level: 'warn',
  message: 'Swagger documentation not available:',
  timestamp: '2025-07-10 22:58:13'
}
{
  message: '🚀 Servidor NFSe Fiorilli iniciado na porta 3000',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-10 23:12:14'
}
{
  message: '📚 Documentação disponível em: http://localhost:3000/api-docs',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-10 23:12:14'
}
{
  message: '🏥 Health check disponível em: http://localhost:3000/health',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-10 23:12:14'
}
{
  message: '🌍 Ambiente: development',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-10 23:12:14'
}
{
  message: '🚀 Servidor NFSe Fiorilli iniciado na porta 3000',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:15:10'
}
{
  message: '📚 Documentação disponível em: http://localhost:3000/api-docs',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:15:10'
}
{
  message: '🏥 Health check disponível em: http://localhost:3000/health',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:15:10'
}
{
  message: '🌍 Ambiente: development',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:15:10'
}
{
  message: '::1 - - [11/Jul/2025:14:15:16 +0000] "GET /api-docs HTTP/1.1" 301 158 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:15:16'
}
{
  message: '::1 - - [11/Jul/2025:14:15:16 +0000] "GET /api-docs/ HTTP/1.1" 200 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:15:16'
}
{
  message: '::1 - - [11/Jul/2025:14:15:16 +0000] "GET /api-docs/swagger-ui-init.js HTTP/1.1" 200 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:15:16'
}
{
  message: '::1 - - [11/Jul/2025:14:15:16 +0000] "GET /api-docs/swagger-ui.css HTTP/1.1" 200 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:15:16'
}
{
  message: '::1 - - [11/Jul/2025:14:15:16 +0000] "GET /api-docs/swagger-ui-standalone-preset.js HTTP/1.1" 200 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:15:16'
}
{
  message: '::1 - - [11/Jul/2025:14:15:16 +0000] "GET /api-docs/swagger-ui-bundle.js HTTP/1.1" 200 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:15:16'
}
{
  message: '::1 - - [11/Jul/2025:14:15:16 +0000] "GET /api-docs/favicon-32x32.png HTTP/1.1" 200 628 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:15:16'
}
{
  message: '::1 - - [11/Jul/2025:14:15:18 +0000] "GET /firebase-messaging-sw.js HTTP/1.1" 404 125 "http://localhost:3000/firebase-messaging-sw.js" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:15:18'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  rpsNumero: 1,
  prestadorCnpj: '56135432000164',
  level: 'info',
  message: 'Iniciando geração de NFSe',
  timestamp: '2025-07-11 10:16:05'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  error: "Cannot read properties of undefined (reading 'xmlBuilder')",
  stack: "TypeError: Cannot read properties of undefined (reading 'xmlBuilder')\n" +
    '    at gerarNfse (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/src/controllers/nfse-controller.js:27:27)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at next (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:149:13)\n' +
    '    at validateGerarNfse (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/src/middleware/validation.js:265:3)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at next (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:149:13)\n' +
    '    at Route.dispatch (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at /home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:284:15\n' +
    '    at Function.process_params (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:346:12)',
  rpsData: {
    Id: 'RPS001_2025',
    Numero: 1,
    Serie: '001',
    Tipo: 1,
    DataEmissao: '2025-06-02T02:46:19.000Z',
    NaturezaOperacao: 1,
    OptanteSimplesNacional: 1,
    IncentivadorCultural: 2,
    Status: 1,
    Servico: {
      ValorServicos: 7000,
      ValorDeducoes: 0,
      ValorPis: 0,
      ValorCofins: 0,
      ValorInss: 0,
      ValorIr: 0,
      ValorCsll: 0,
      IssRetido: 2,
      ValorIss: 140.7,
      ValorIssRetido: 0,
      OutrasRetencoes: 0,
      BaseCalculo: 7000,
      Aliquota: 2.01,
      ValorLiquidoNfse: 7000,
      DescontoIncondicionado: 0,
      DescontoCondicionado: 0,
      ItemListaServico: '01.08',
      CodigoCnae: '6319400',
      CodigoTributacaoMunicipio: '000001.0000008',
      Discriminacao: 'Planejamento, confecção, manutenção e atualização de páginas eletrônicas',
      CodigoMunicipio: '5105622'
    },
    Prestador: { Cnpj: '56135432000164', InscricaoMunicipal: '000041600' },
    Tomador: {
      Cnpj: '33000071000143',
      RazaoSocial: 'QI PLUS SISTEMAS LTDA',
      Endereco: {
        Endereco: 'R PACAEMBU',
        Numero: '30',
        Complemento: 'SALA 08',
        Bairro: 'RESIDENCIAL PACAEMBU',
        CodigoMunicipio: '3523909',
        Uf: 'SP',
        Cep: '13295468'
      }
    }
  },
  level: 'error',
  message: 'Erro ao gerar NFSe',
  timestamp: '2025-07-11 10:16:05'
}
{
  message: '::1 - - [11/Jul/2025:14:16:05 +0000] "POST /api/nfse/gerar HTTP/1.1" 500 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 10:16:05'
}
{
  message: '🚀 Servidor NFSe Fiorilli iniciado na porta 3000',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:53:46'
}
{
  message: '📚 Documentação disponível em: http://localhost:3000/api-docs',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:53:46'
}
{
  message: '🏥 Health check disponível em: http://localhost:3000/health',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:53:46'
}
{
  message: '🌍 Ambiente: development',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:53:46'
}
{
  message: '::1 - - [12/Jul/2025:03:53:49 +0000] "GET /health HTTP/1.1" 200 121 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:53:49'
}
{
  message: '::1 - - [12/Jul/2025:03:53:49 +0000] "GET /favicon.ico HTTP/1.1" 404 112 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:53:49'
}
{
  message: '::1 - - [12/Jul/2025:03:53:51 +0000] "GET /firebase-messaging-sw.js HTTP/1.1" 404 125 "http://localhost:3000/firebase-messaging-sw.js" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:53:51'
}
{
  message: '::1 - - [12/Jul/2025:03:53:59 +0000] "GET /health HTTP/1.1" 200 122 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:53:59'
}
{
  message: '::1 - - [12/Jul/2025:03:54:01 +0000] "GET /api-docs/ HTTP/1.1" 304 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:54:01'
}
{
  message: '::1 - - [12/Jul/2025:03:54:01 +0000] "GET /api-docs/swagger-ui.css HTTP/1.1" 304 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:54:01'
}
{
  message: '::1 - - [12/Jul/2025:03:54:01 +0000] "GET /api-docs/swagger-ui-init.js HTTP/1.1" 304 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:54:01'
}
{
  message: '::1 - - [12/Jul/2025:03:54:01 +0000] "GET /api-docs/swagger-ui-bundle.js HTTP/1.1" 304 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:54:01'
}
{
  message: '::1 - - [12/Jul/2025:03:54:01 +0000] "GET /api-docs/swagger-ui-standalone-preset.js HTTP/1.1" 304 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:54:01'
}
{
  message: '::1 - - [12/Jul/2025:03:54:01 +0000] "GET /firebase-messaging-sw.js HTTP/1.1" 404 125 "http://localhost:3000/firebase-messaging-sw.js" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:54:01'
}
{
  message: '::1 - - [12/Jul/2025:03:54:03 +0000] "GET /firebase-messaging-sw.js HTTP/1.1" 404 125 "http://localhost:3000/firebase-messaging-sw.js" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-11 23:54:03'
}
{
  message: 'Recebido SIGINT, encerrando logger...',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:01:10'
}
{
  message: '🚀 Servidor NFSe Fiorilli iniciado na porta 3000',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:01:12'
}
{
  message: '📚 Documentação disponível em: http://localhost:3000/api-docs',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:01:12'
}
{
  message: '🏥 Health check disponível em: http://localhost:3000/health',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:01:12'
}
{
  message: '🌍 Ambiente: development',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:01:12'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  errors: [
    {
      field: 'Servico.ValorLiquidoNfse',
      message: 'Valor líquido calculado (1000.00) não confere com o informado (950)',
      expected: '1000.00',
      received: 950
    }
  ],
  body: {
    Id: 'RPS001_2025',
    Numero: 1,
    Serie: '001',
    Tipo: 1,
    DataEmissao: '2025-06-02T02:46:19.000Z',
    NaturezaOperacao: 1,
    OptanteSimplesNacional: 1,
    IncentivadorCultural: 2,
    Status: 1,
    Servico: {
      ValorServicos: 1000,
      BaseCalculo: 1000,
      Aliquota: 5,
      ValorIss: 50,
      ValorLiquidoNfse: 950,
      ItemListaServico: '01.01',
      Discriminacao: 'Desenvolvimento de software',
      CodigoMunicipio: '3550308',
      ValorDeducoes: 0,
      ValorPis: 0,
      ValorCofins: 0,
      ValorInss: 0,
      ValorIr: 0,
      ValorCsll: 0,
      IssRetido: 2,
      ValorIssRetido: 0,
      OutrasRetencoes: 0,
      DescontoIncondicionado: 0,
      DescontoCondicionado: 0
    },
    Prestador: { Cnpj: '12345678000195', InscricaoMunicipal: '123456' },
    Tomador: { Cnpj: '98765432000198', RazaoSocial: 'EMPRESA CLIENTE LTDA' }
  },
  level: 'warn',
  message: 'Validação customizada falhou',
  timestamp: '2025-07-12 00:02:54'
}
{
  message: '::1 - - [12/Jul/2025:04:02:54 +0000] "POST /api/nfse/gerar HTTP/1.1" 400 238 "-" "PostmanRuntime/7.43.0"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:02:54'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  rpsNumero: 1,
  prestadorCnpj: '12345678000195',
  level: 'info',
  message: 'Iniciando geração de NFSe',
  timestamp: '2025-07-12 00:03:26'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  error: "Cannot read properties of undefined (reading 'xmlBuilder')",
  stack: "TypeError: Cannot read properties of undefined (reading 'xmlBuilder')\n" +
    '    at gerarNfse (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/src/controllers/nfse-controller.js:27:27)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at next (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:149:13)\n' +
    '    at validateGerarNfse (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/src/middleware/validation.js:265:3)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at next (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:149:13)\n' +
    '    at Route.dispatch (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at /home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:284:15\n' +
    '    at Function.process_params (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:346:12)',
  rpsData: {
    Id: 'RPS001_2025',
    Numero: 1,
    Serie: '001',
    Tipo: 1,
    DataEmissao: '2025-06-02T02:46:19.000Z',
    NaturezaOperacao: 1,
    OptanteSimplesNacional: 1,
    IncentivadorCultural: 2,
    Status: 1,
    Servico: {
      ValorServicos: 1000,
      BaseCalculo: 1000,
      Aliquota: 5,
      ValorIss: 50,
      ValorLiquidoNfse: 1000,
      ItemListaServico: '01.01',
      Discriminacao: 'Desenvolvimento de software',
      CodigoMunicipio: '3550308',
      ValorDeducoes: 0,
      ValorPis: 0,
      ValorCofins: 0,
      ValorInss: 0,
      ValorIr: 0,
      ValorCsll: 0,
      IssRetido: 2,
      ValorIssRetido: 0,
      OutrasRetencoes: 0,
      DescontoIncondicionado: 0,
      DescontoCondicionado: 0
    },
    Prestador: { Cnpj: '12345678000195', InscricaoMunicipal: '123456' },
    Tomador: { Cnpj: '98765432000198', RazaoSocial: 'EMPRESA CLIENTE LTDA' }
  },
  level: 'error',
  message: 'Erro ao gerar NFSe',
  timestamp: '2025-07-12 00:03:26'
}
{
  message: '::1 - - [12/Jul/2025:04:03:26 +0000] "POST /api/nfse/gerar HTTP/1.1" 500 - "-" "PostmanRuntime/7.43.0"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:03:26'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  numero: '19',
  cnpjPrestador: '56135432000164',
  inscricaoMunicipal: '000041600',
  level: 'info',
  message: 'Consultando NFSe por número',
  timestamp: '2025-07-12 00:04:19'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  error: "Cannot read properties of undefined (reading 'xmlBuilder')",
  numero: '19',
  level: 'error',
  message: 'Erro ao consultar NFSe',
  timestamp: '2025-07-12 00:04:19'
}
{
  message: '::1 - - [12/Jul/2025:04:04:19 +0000] "GET /api/nfse/consultar/19?cnpjPrestador=56135432000164&inscricaoMunicipal=000041600 HTTP/1.1" 500 - "-" "PostmanRuntime/7.43.0"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:04:19'
}
{
  message: '🚀 Servidor NFSe Fiorilli iniciado na porta 3000',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:25:47'
}
{
  message: '📚 Documentação disponível em: http://localhost:3000/api-docs',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:25:47'
}
{
  message: '🏥 Health check disponível em: http://localhost:3000/health',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:25:47'
}
{
  message: '🌍 Ambiente: development',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:25:47'
}
{
  message: '🚀 Servidor NFSe Fiorilli iniciado na porta 3000',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:25:56'
}
{
  message: '📚 Documentação disponível em: http://localhost:3000/api-docs',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:25:56'
}
{
  message: '🏥 Health check disponível em: http://localhost:3000/health',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:25:56'
}
{
  message: '🌍 Ambiente: development',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:25:56'
}
{
  message: '🚀 Servidor NFSe Fiorilli iniciado na porta 3000',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:26:13'
}
{
  message: '📚 Documentação disponível em: http://localhost:3000/api-docs',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:26:13'
}
{
  message: '🏥 Health check disponível em: http://localhost:3000/health',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:26:13'
}
{
  message: '🌍 Ambiente: development',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:26:13'
}
{
  message: '🚀 Servidor NFSe Fiorilli iniciado na porta 3000',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:26:24'
}
{
  message: '📚 Documentação disponível em: http://localhost:3000/api-docs',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:26:24'
}
{
  message: '🏥 Health check disponível em: http://localhost:3000/health',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:26:24'
}
{
  message: '🌍 Ambiente: development',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:26:24'
}
{
  message: 'Recebido SIGTERM, encerrando logger...',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:26:54'
}
{
  message: '🚀 Servidor NFSe Fiorilli iniciado na porta 3000',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:27:06'
}
{
  message: '📚 Documentação disponível em: http://localhost:3000/api-docs',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:27:06'
}
{
  message: '🏥 Health check disponível em: http://localhost:3000/health',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:27:06'
}
{
  message: '🌍 Ambiente: development',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:27:06'
}
{
  message: '::1 - - [12/Jul/2025:04:27:26 +0000] "GET /api-docs/ HTTP/1.1" 304 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:27:26'
}
{
  message: '::1 - - [12/Jul/2025:04:27:26 +0000] "GET /api-docs/swagger-ui.css HTTP/1.1" 304 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:27:26'
}
{
  message: '::1 - - [12/Jul/2025:04:27:26 +0000] "GET /api-docs/swagger-ui-bundle.js HTTP/1.1" 304 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:27:26'
}
{
  message: '::1 - - [12/Jul/2025:04:27:26 +0000] "GET /api-docs/swagger-ui-standalone-preset.js HTTP/1.1" 304 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:27:26'
}
{
  message: '::1 - - [12/Jul/2025:04:27:26 +0000] "GET /api-docs/swagger-ui-init.js HTTP/1.1" 304 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:27:26'
}
{
  message: '::1 - - [12/Jul/2025:04:27:27 +0000] "GET /api-docs/favicon-32x32.png HTTP/1.1" 304 - "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:27:27'
}
{
  message: '::1 - - [12/Jul/2025:04:27:28 +0000] "GET /firebase-messaging-sw.js HTTP/1.1" 404 125 "http://localhost:3000/firebase-messaging-sw.js" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:27:28'
}
{
  message: '::ffff:127.0.0.1 - - [12/Jul/2025:04:27:32 +0000] "GET /api/nfse/exemplo HTTP/1.1" 200 1421 "-" "curl/7.81.0"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:27:32'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  errors: [
    {
      field: 'Servico.ValorLiquidoNfse',
      message: 'Valor líquido calculado (1000.00) não confere com o informado (950)',
      expected: '1000.00',
      received: 950
    }
  ],
  body: {
    Id: 'RPS_TESTE_001',
    Numero: 1,
    Serie: '001',
    Tipo: 1,
    DataEmissao: '2025-07-12T04:27:32.805Z',
    NaturezaOperacao: 1,
    OptanteSimplesNacional: 1,
    IncentivadorCultural: 2,
    Status: 1,
    Servico: {
      ValorServicos: 1000,
      ValorDeducoes: 0,
      ValorPis: 0,
      ValorCofins: 0,
      ValorInss: 0,
      ValorIr: 0,
      ValorCsll: 0,
      IssRetido: 2,
      ValorIss: 50,
      ValorIssRetido: 0,
      OutrasRetencoes: 0,
      BaseCalculo: 1000,
      Aliquota: 5,
      ValorLiquidoNfse: 950,
      DescontoIncondicionado: 0,
      DescontoCondicionado: 0,
      ItemListaServico: '01.01',
      CodigoCnae: '6201500',
      CodigoTributacaoMunicipio: '010101',
      Discriminacao: 'Desenvolvimento de software sob medida para teste',
      CodigoMunicipio: '3550308'
    },
    Prestador: { Cnpj: '01001001000113', InscricaoMunicipal: '15000' },
    Tomador: {
      Cnpj: '98765432000198',
      InscricaoMunicipal: '654321',
      RazaoSocial: 'Empresa Tomadora LTDA',
      Endereco: {
        Endereco: 'Rua das Flores',
        Numero: '123',
        Complemento: 'Sala 1',
        Bairro: 'Centro',
        CodigoMunicipio: '3550308',
        Uf: 'SP',
        Cep: '01234567'
      },
      Contato: { Telefone: '11999999999', Email: '<EMAIL>' }
    }
  },
  level: 'warn',
  message: 'Validação customizada falhou',
  timestamp: '2025-07-12 00:27:55'
}
{
  message: '::ffff:127.0.0.1 - - [12/Jul/2025:04:27:55 +0000] "POST /api/nfse/gerar HTTP/1.1" 400 238 "-" "curl/7.81.0"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:27:55'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  rpsNumero: 1,
  prestadorCnpj: '01001001000113',
  level: 'info',
  message: 'Iniciando geração de NFSe',
  timestamp: '2025-07-12 00:28:15'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  error: 'name.indexOf is not a function',
  rpsData: {
    Id: 'RPS_TESTE_001',
    Numero: 1,
    Serie: '001',
    Tipo: 1,
    DataEmissao: '2025-07-12T04:27:32.805Z',
    NaturezaOperacao: 1,
    OptanteSimplesNacional: 1,
    IncentivadorCultural: 2,
    Status: 1,
    Servico: {
      ValorServicos: 1000,
      ValorDeducoes: 0,
      ValorPis: 0,
      ValorCofins: 0,
      ValorInss: 0,
      ValorIr: 0,
      ValorCsll: 0,
      IssRetido: 2,
      ValorIss: 50,
      ValorIssRetido: 0,
      OutrasRetencoes: 0,
      BaseCalculo: 1000,
      Aliquota: 5,
      ValorLiquidoNfse: 1000,
      DescontoIncondicionado: 0,
      DescontoCondicionado: 0,
      ItemListaServico: '01.01',
      CodigoCnae: '6201500',
      CodigoTributacaoMunicipio: '010101',
      Discriminacao: 'Desenvolvimento de software sob medida para teste',
      CodigoMunicipio: '3550308'
    },
    Prestador: { Cnpj: '01001001000113', InscricaoMunicipal: '15000' },
    Tomador: {
      Cnpj: '98765432000198',
      InscricaoMunicipal: '654321',
      RazaoSocial: 'Empresa Tomadora LTDA',
      Endereco: {
        Endereco: 'Rua das Flores',
        Numero: '123',
        Complemento: 'Sala 1',
        Bairro: 'Centro',
        CodigoMunicipio: '3550308',
        Uf: 'SP',
        Cep: '01234567'
      },
      Contato: { Telefone: '11999999999', Email: '<EMAIL>' }
    }
  },
  level: 'error',
  message: 'Erro ao construir XML GerarNfse',
  timestamp: '2025-07-12 00:28:15'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  error: 'Erro ao construir XML: name.indexOf is not a function',
  stack: 'Error: Erro ao construir XML: name.indexOf is not a function\n' +
    '    at XmlBuilder.buildGerarNfseXml (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/src/services/xml-builder.js:53:13)\n' +
    '    at gerarNfse (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/src/controllers/nfse-controller.js:25:31)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at next (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:149:13)\n' +
    '    at validateGerarNfse (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/src/middleware/validation.js:265:3)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at next (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:149:13)\n' +
    '    at Route.dispatch (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at /home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:284:15',
  rpsData: {
    Id: 'RPS_TESTE_001',
    Numero: 1,
    Serie: '001',
    Tipo: 1,
    DataEmissao: '2025-07-12T04:27:32.805Z',
    NaturezaOperacao: 1,
    OptanteSimplesNacional: 1,
    IncentivadorCultural: 2,
    Status: 1,
    Servico: {
      ValorServicos: 1000,
      ValorDeducoes: 0,
      ValorPis: 0,
      ValorCofins: 0,
      ValorInss: 0,
      ValorIr: 0,
      ValorCsll: 0,
      IssRetido: 2,
      ValorIss: 50,
      ValorIssRetido: 0,
      OutrasRetencoes: 0,
      BaseCalculo: 1000,
      Aliquota: 5,
      ValorLiquidoNfse: 1000,
      DescontoIncondicionado: 0,
      DescontoCondicionado: 0,
      ItemListaServico: '01.01',
      CodigoCnae: '6201500',
      CodigoTributacaoMunicipio: '010101',
      Discriminacao: 'Desenvolvimento de software sob medida para teste',
      CodigoMunicipio: '3550308'
    },
    Prestador: { Cnpj: '01001001000113', InscricaoMunicipal: '15000' },
    Tomador: {
      Cnpj: '98765432000198',
      InscricaoMunicipal: '654321',
      RazaoSocial: 'Empresa Tomadora LTDA',
      Endereco: {
        Endereco: 'Rua das Flores',
        Numero: '123',
        Complemento: 'Sala 1',
        Bairro: 'Centro',
        CodigoMunicipio: '3550308',
        Uf: 'SP',
        Cep: '01234567'
      },
      Contato: { Telefone: '11999999999', Email: '<EMAIL>' }
    }
  },
  level: 'error',
  message: 'Erro ao gerar NFSe',
  timestamp: '2025-07-12 00:28:15'
}
{
  message: '::ffff:127.0.0.1 - - [12/Jul/2025:04:28:15 +0000] "POST /api/nfse/gerar HTTP/1.1" 500 1785 "-" "curl/7.81.0"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:28:15'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  rpsNumero: 1,
  prestadorCnpj: '01001001000113',
  level: 'info',
  message: 'Iniciando geração de NFSe',
  timestamp: '2025-07-12 00:28:53'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  error: 'name.indexOf is not a function',
  rpsData: {
    Id: 'RPS_TESTE_001',
    Numero: 1,
    Serie: '001',
    Tipo: 1,
    DataEmissao: '2025-07-12T04:27:32.805Z',
    NaturezaOperacao: 1,
    OptanteSimplesNacional: 1,
    IncentivadorCultural: 2,
    Status: 1,
    Servico: {
      ValorServicos: 1000,
      ValorDeducoes: 0,
      ValorPis: 0,
      ValorCofins: 0,
      ValorInss: 0,
      ValorIr: 0,
      ValorCsll: 0,
      IssRetido: 2,
      ValorIss: 50,
      ValorIssRetido: 0,
      OutrasRetencoes: 0,
      BaseCalculo: 1000,
      Aliquota: 5,
      ValorLiquidoNfse: 1000,
      DescontoIncondicionado: 0,
      DescontoCondicionado: 0,
      ItemListaServico: '01.01',
      CodigoCnae: '6201500',
      CodigoTributacaoMunicipio: '010101',
      Discriminacao: 'Desenvolvimento de software sob medida para teste',
      CodigoMunicipio: '3550308'
    },
    Prestador: { Cnpj: '01001001000113', InscricaoMunicipal: '15000' },
    Tomador: {
      Cnpj: '98765432000198',
      InscricaoMunicipal: '654321',
      RazaoSocial: 'Empresa Tomadora LTDA',
      Endereco: {
        Endereco: 'Rua das Flores',
        Numero: '123',
        Complemento: 'Sala 1',
        Bairro: 'Centro',
        CodigoMunicipio: '3550308',
        Uf: 'SP',
        Cep: '01234567'
      },
      Contato: { Telefone: '11999999999', Email: '<EMAIL>' }
    }
  },
  level: 'error',
  message: 'Erro ao construir XML GerarNfse',
  timestamp: '2025-07-12 00:28:53'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  error: 'Erro ao construir XML: name.indexOf is not a function',
  stack: 'Error: Erro ao construir XML: name.indexOf is not a function\n' +
    '    at XmlBuilder.buildGerarNfseXml (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/src/services/xml-builder.js:53:13)\n' +
    '    at gerarNfse (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/src/controllers/nfse-controller.js:25:31)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at next (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:149:13)\n' +
    '    at validateGerarNfse (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/src/middleware/validation.js:265:3)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at next (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:149:13)\n' +
    '    at Route.dispatch (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at /home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:284:15',
  rpsData: {
    Id: 'RPS_TESTE_001',
    Numero: 1,
    Serie: '001',
    Tipo: 1,
    DataEmissao: '2025-07-12T04:27:32.805Z',
    NaturezaOperacao: 1,
    OptanteSimplesNacional: 1,
    IncentivadorCultural: 2,
    Status: 1,
    Servico: {
      ValorServicos: 1000,
      ValorDeducoes: 0,
      ValorPis: 0,
      ValorCofins: 0,
      ValorInss: 0,
      ValorIr: 0,
      ValorCsll: 0,
      IssRetido: 2,
      ValorIss: 50,
      ValorIssRetido: 0,
      OutrasRetencoes: 0,
      BaseCalculo: 1000,
      Aliquota: 5,
      ValorLiquidoNfse: 1000,
      DescontoIncondicionado: 0,
      DescontoCondicionado: 0,
      ItemListaServico: '01.01',
      CodigoCnae: '6201500',
      CodigoTributacaoMunicipio: '010101',
      Discriminacao: 'Desenvolvimento de software sob medida para teste',
      CodigoMunicipio: '3550308'
    },
    Prestador: { Cnpj: '01001001000113', InscricaoMunicipal: '15000' },
    Tomador: {
      Cnpj: '98765432000198',
      InscricaoMunicipal: '654321',
      RazaoSocial: 'Empresa Tomadora LTDA',
      Endereco: {
        Endereco: 'Rua das Flores',
        Numero: '123',
        Complemento: 'Sala 1',
        Bairro: 'Centro',
        CodigoMunicipio: '3550308',
        Uf: 'SP',
        Cep: '01234567'
      },
      Contato: { Telefone: '11999999999', Email: '<EMAIL>' }
    }
  },
  level: 'error',
  message: 'Erro ao gerar NFSe',
  timestamp: '2025-07-12 00:28:53'
}
{
  message: '::ffff:127.0.0.1 - - [12/Jul/2025:04:28:53 +0000] "POST /api/nfse/gerar HTTP/1.1" 500 1785 "-" "curl/7.81.0"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:28:53'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  rpsNumero: 1,
  prestadorCnpj: '01001001000113',
  level: 'info',
  message: 'Iniciando geração de NFSe',
  timestamp: '2025-07-12 00:29:41'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  error: 'name.indexOf is not a function',
  rpsData: {
    Id: 'RPS_TESTE_001',
    Numero: 1,
    Serie: '001',
    Tipo: 1,
    DataEmissao: '2025-07-12T04:27:32.805Z',
    NaturezaOperacao: 1,
    OptanteSimplesNacional: 1,
    IncentivadorCultural: 2,
    Status: 1,
    Servico: {
      ValorServicos: 1000,
      ValorDeducoes: 0,
      ValorPis: 0,
      ValorCofins: 0,
      ValorInss: 0,
      ValorIr: 0,
      ValorCsll: 0,
      IssRetido: 2,
      ValorIss: 50,
      ValorIssRetido: 0,
      OutrasRetencoes: 0,
      BaseCalculo: 1000,
      Aliquota: 5,
      ValorLiquidoNfse: 1000,
      DescontoIncondicionado: 0,
      DescontoCondicionado: 0,
      ItemListaServico: '01.01',
      CodigoCnae: '6201500',
      CodigoTributacaoMunicipio: '010101',
      Discriminacao: 'Desenvolvimento de software sob medida para teste',
      CodigoMunicipio: '3550308'
    },
    Prestador: { Cnpj: '01001001000113', InscricaoMunicipal: '15000' },
    Tomador: {
      Cnpj: '98765432000198',
      InscricaoMunicipal: '654321',
      RazaoSocial: 'Empresa Tomadora LTDA',
      Endereco: {
        Endereco: 'Rua das Flores',
        Numero: '123',
        Complemento: 'Sala 1',
        Bairro: 'Centro',
        CodigoMunicipio: '3550308',
        Uf: 'SP',
        Cep: '01234567'
      },
      Contato: { Telefone: '11999999999', Email: '<EMAIL>' }
    }
  },
  level: 'error',
  message: 'Erro ao construir XML GerarNfse',
  timestamp: '2025-07-12 00:29:41'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  error: 'Erro ao construir XML: name.indexOf is not a function',
  stack: 'Error: Erro ao construir XML: name.indexOf is not a function\n' +
    '    at XmlBuilder.buildGerarNfseXml (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/src/services/xml-builder.js:53:13)\n' +
    '    at gerarNfse (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/src/controllers/nfse-controller.js:25:31)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at next (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:149:13)\n' +
    '    at validateGerarNfse (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/src/middleware/validation.js:265:3)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at next (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:149:13)\n' +
    '    at Route.dispatch (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at /home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:284:15',
  rpsData: {
    Id: 'RPS_TESTE_001',
    Numero: 1,
    Serie: '001',
    Tipo: 1,
    DataEmissao: '2025-07-12T04:27:32.805Z',
    NaturezaOperacao: 1,
    OptanteSimplesNacional: 1,
    IncentivadorCultural: 2,
    Status: 1,
    Servico: {
      ValorServicos: 1000,
      ValorDeducoes: 0,
      ValorPis: 0,
      ValorCofins: 0,
      ValorInss: 0,
      ValorIr: 0,
      ValorCsll: 0,
      IssRetido: 2,
      ValorIss: 50,
      ValorIssRetido: 0,
      OutrasRetencoes: 0,
      BaseCalculo: 1000,
      Aliquota: 5,
      ValorLiquidoNfse: 1000,
      DescontoIncondicionado: 0,
      DescontoCondicionado: 0,
      ItemListaServico: '01.01',
      CodigoCnae: '6201500',
      CodigoTributacaoMunicipio: '010101',
      Discriminacao: 'Desenvolvimento de software sob medida para teste',
      CodigoMunicipio: '3550308'
    },
    Prestador: { Cnpj: '01001001000113', InscricaoMunicipal: '15000' },
    Tomador: {
      Cnpj: '98765432000198',
      InscricaoMunicipal: '654321',
      RazaoSocial: 'Empresa Tomadora LTDA',
      Endereco: {
        Endereco: 'Rua das Flores',
        Numero: '123',
        Complemento: 'Sala 1',
        Bairro: 'Centro',
        CodigoMunicipio: '3550308',
        Uf: 'SP',
        Cep: '01234567'
      },
      Contato: { Telefone: '11999999999', Email: '<EMAIL>' }
    }
  },
  level: 'error',
  message: 'Erro ao gerar NFSe',
  timestamp: '2025-07-12 00:29:41'
}
{
  message: '::ffff:127.0.0.1 - - [12/Jul/2025:04:29:41 +0000] "POST /api/nfse/gerar HTTP/1.1" 500 1785 "-" "curl/7.81.0"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:29:41'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  rpsNumero: 1,
  prestadorCnpj: '01001001000113',
  level: 'info',
  message: 'Iniciando geração de NFSe',
  timestamp: '2025-07-12 00:30:15'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  error: 'name.indexOf is not a function',
  rpsData: {
    Id: 'RPS_TESTE_001',
    Numero: 1,
    Serie: '001',
    Tipo: 1,
    DataEmissao: '2025-07-12T04:27:32.805Z',
    NaturezaOperacao: 1,
    OptanteSimplesNacional: 1,
    IncentivadorCultural: 2,
    Status: 1,
    Servico: {
      ValorServicos: 1000,
      ValorDeducoes: 0,
      ValorPis: 0,
      ValorCofins: 0,
      ValorInss: 0,
      ValorIr: 0,
      ValorCsll: 0,
      IssRetido: 2,
      ValorIss: 50,
      ValorIssRetido: 0,
      OutrasRetencoes: 0,
      BaseCalculo: 1000,
      Aliquota: 5,
      ValorLiquidoNfse: 1000,
      DescontoIncondicionado: 0,
      DescontoCondicionado: 0,
      ItemListaServico: '01.01',
      CodigoCnae: '6201500',
      CodigoTributacaoMunicipio: '010101',
      Discriminacao: 'Desenvolvimento de software sob medida para teste',
      CodigoMunicipio: '3550308'
    },
    Prestador: { Cnpj: '01001001000113', InscricaoMunicipal: '15000' },
    Tomador: {
      Cnpj: '98765432000198',
      InscricaoMunicipal: '654321',
      RazaoSocial: 'Empresa Tomadora LTDA',
      Endereco: {
        Endereco: 'Rua das Flores',
        Numero: '123',
        Complemento: 'Sala 1',
        Bairro: 'Centro',
        CodigoMunicipio: '3550308',
        Uf: 'SP',
        Cep: '01234567'
      },
      Contato: { Telefone: '11999999999', Email: '<EMAIL>' }
    }
  },
  level: 'error',
  message: 'Erro ao construir XML GerarNfse',
  timestamp: '2025-07-12 00:30:15'
}
{
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  error: 'Erro ao construir XML: name.indexOf is not a function',
  stack: 'Error: Erro ao construir XML: name.indexOf is not a function\n' +
    '    at XmlBuilder.buildGerarNfseXml (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/src/services/xml-builder.js:53:13)\n' +
    '    at gerarNfse (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/src/controllers/nfse-controller.js:25:31)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at next (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:149:13)\n' +
    '    at validateGerarNfse (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/src/middleware/validation.js:265:3)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at next (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:149:13)\n' +
    '    at Route.dispatch (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (/home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/layer.js:95:5)\n' +
    '    at /home/<USER>/Downloads/workspace-decf5ef2-03ad-4aad-934c-e1d38fe4c626-2025-07-10/node_modules/express/lib/router/index.js:284:15',
  rpsData: {
    Id: 'RPS_TESTE_001',
    Numero: 1,
    Serie: '001',
    Tipo: 1,
    DataEmissao: '2025-07-12T04:27:32.805Z',
    NaturezaOperacao: 1,
    OptanteSimplesNacional: 1,
    IncentivadorCultural: 2,
    Status: 1,
    Servico: {
      ValorServicos: 1000,
      ValorDeducoes: 0,
      ValorPis: 0,
      ValorCofins: 0,
      ValorInss: 0,
      ValorIr: 0,
      ValorCsll: 0,
      IssRetido: 2,
      ValorIss: 50,
      ValorIssRetido: 0,
      OutrasRetencoes: 0,
      BaseCalculo: 1000,
      Aliquota: 5,
      ValorLiquidoNfse: 1000,
      DescontoIncondicionado: 0,
      DescontoCondicionado: 0,
      ItemListaServico: '01.01',
      CodigoCnae: '6201500',
      CodigoTributacaoMunicipio: '010101',
      Discriminacao: 'Desenvolvimento de software sob medida para teste',
      CodigoMunicipio: '3550308'
    },
    Prestador: { Cnpj: '01001001000113', InscricaoMunicipal: '15000' },
    Tomador: {
      Cnpj: '98765432000198',
      InscricaoMunicipal: '654321',
      RazaoSocial: 'Empresa Tomadora LTDA',
      Endereco: {
        Endereco: 'Rua das Flores',
        Numero: '123',
        Complemento: 'Sala 1',
        Bairro: 'Centro',
        CodigoMunicipio: '3550308',
        Uf: 'SP',
        Cep: '01234567'
      },
      Contato: { Telefone: '11999999999', Email: '<EMAIL>' }
    }
  },
  level: 'error',
  message: 'Erro ao gerar NFSe',
  timestamp: '2025-07-12 00:30:15'
}
{
  message: '::ffff:127.0.0.1 - - [12/Jul/2025:04:30:15 +0000] "POST /api/nfse/gerar HTTP/1.1" 500 1785 "-" "curl/7.81.0"',
  level: 'info',
  service: 'fiorilli-nfse-microserver',
  version: '1.0.0',
  environment: 'development',
  timestamp: '2025-07-12 00:30:15'
}
